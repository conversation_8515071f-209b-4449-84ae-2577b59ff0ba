@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 47.4% 11.2%;

    --radius: 0.5rem;

    /* Sidebar specific variables */
    --sidebar-background: 210 40% 96.1%;
    --sidebar-foreground: 222.2 47.4% 11.2%;
    --sidebar-primary: 222.2 47.4% 11.2%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 222.2 47.4% 11.2%;
  }

  .dark {
    --background: 220 20% 5%; /* Even Darker background */
    --foreground: 210 40% 98%;

    --card: 220 20% 8%; /* Even Darker card background */
    --card-foreground: 210 40% 98%;

    --popover: 220 20% 8%; /* Even Darker popover background */
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 220 20% 5%;

    --secondary: 217.2 32.6% 12%; /* Darker secondary */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 12%; /* Darker muted */
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 12%; /* Darker accent */
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 20%; /* Darker border */
    --input: 217.2 32.6% 20%; /* Darker input border */
    --ring: 212.7 26.8% 83.9%;

    /* Sidebar specific variables for dark mode */
    --sidebar-background: 220 20% 10%; /* Even Darker sidebar background */
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 98%;
    --sidebar-primary-foreground: 220 20% 10%;
    --sidebar-accent: 217.2 32.6% 15%; /* Darker sidebar accent */
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 20%; /* Darker sidebar border */
    --sidebar-ring: 212.7 26.8% 83.9%;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/20 dark:bg-black/40 backdrop-blur-xl border border-white/30 dark:border-white/25 shadow-lg;
  }

  .glass-card-hover {
    @apply hover:bg-white/30 dark:hover:bg-black/50 hover:border-white/40 dark:hover:border-white/35 transition-all duration-300 hover:shadow-xl;
  }

  .glass-interactive {
    @apply bg-white/10 dark:bg-black/30 hover:bg-white/20 dark:hover:bg-black/40 transition-colors duration-200;
  }

  .glass-sidebar {
    @apply bg-white/20 dark:bg-black/40 backdrop-blur-xl border-r border-white/30 dark:border-white/25 shadow-lg;
  }

  .glass-sidebar-item {
    @apply bg-white/10 dark:bg-black/30 hover:bg-white/20 dark:hover:bg-black/40 transition-colors duration-200;
  }

  .glass-sidebar-item-active {
    @apply bg-white/30 dark:bg-black/50;
}

@layer components {
  .glass-card {
    @apply bg-white/20 dark:bg-black/30 backdrop-blur-xl border border-white/30 dark:border-white/20 shadow-lg;
  }

  .glass-card-hover {
    @apply hover:bg-white/30 dark:hover:bg-black/40 hover:border-white/40 dark:hover:border-white/30 transition-all duration-300 hover:shadow-xl;
  }

  .glass-interactive {
    @apply bg-white/10 dark:bg-black/20 hover:bg-white/20 dark:hover:bg-black/30 transition-colors duration-200;
  }

  .glass-sidebar {
    @apply bg-white/20 dark:bg-black/30 backdrop-blur-xl border-r border-white/30 dark:border-white/20 shadow-lg;
  }

  .glass-sidebar-item {
    @apply bg-white/10 dark:bg-black/20 hover:bg-white/20 dark:hover:bg-black/30 transition-colors duration-200;
  }

  .glass-sidebar-item-active {
    @apply bg-white/30 dark:bg-black/40;
  }

  .dashboard-background {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--muted)/0.2) 25%,
      hsl(var(--background)) 50%,
      hsl(var(--accent)/0.05) 75%,
      hsl(var(--background)) 100%);
  }

  .dark .dashboard-background {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--muted)/0.2) 25%,
      hsl(var(--background)) 50%,
      hsl(var(--accent)/0.05) 75%,
      hsl(var(--background)) 100%);
  }

  .chart-container {
    @apply bg-card rounded-md p-4 shadow-sm border border-border;
  }

  .filter-dropdown {
    @apply bg-card border border-border rounded-md px-3 py-1.5 text-sm text-card-foreground;
  }
}

/* Compact mode */
.compact-mode .card {
  padding: 0.75rem;
}

.compact-mode .p-4 {
  padding: 0.5rem;
}

.compact-mode .p-6 {
  padding: 0.75rem;
}