
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 92%; /* Light gray instead of pure white */
    --foreground: 222.2 84% 4.9%; /* Dark text */

    --card: 0 0% 95%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 95%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 50%; /* A vibrant blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 88%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 88%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 100% 50%; /* Same as primary for accent */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 82%;
    --input: 214.3 31.8% 82%;
    --ring: 210 100% 50%;

    --radius: 0.5rem;

    /* Sidebar specific colors for light mode */
    --sidebar-background: 210 100% 50%; /* Primary blue for sidebar background */
    --sidebar-foreground: 0 0% 100%; /* White text for sidebar */
    --sidebar-primary: 0 0% 100%; /* White for active item background */
    --sidebar-primary-foreground: 210 100% 50%; /* Blue text for active item */
    --sidebar-accent: 210 100% 60%; /* Lighter blue for hover/active states */
    --sidebar-accent-foreground: 0 0% 100%; /* White text for accent */
    --sidebar-border: 210 100% 40%; /* Darker blue for sidebar border */
    --sidebar-ring: 0 0% 100%; /* White ring for focus */
  }

  .dark {
    --background: 220 15% 10%; /* Dark charcoal background */
    --foreground: 210 20% 98%; /* Light text */

    --card: 220 15% 15%; /* Slightly lighter dark card */
    --card-foreground: 210 20% 98%;

    --popover: 220 15% 15%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 100% 50%; /* Same vibrant blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 220 15% 20%;
    --secondary-foreground: 210 20% 98%;

    --muted: 220 15% 20%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 210 100% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 15% 25%;
    --input: 220 15% 25%;
    --ring: 210 100% 50%;

    /* Sidebar specific colors for dark mode */
    --sidebar-background: 220 15% 10%; /* Dark charcoal for sidebar background */
    --sidebar-foreground: 210 20% 98%; /* Light text for sidebar */
    --sidebar-primary: 210 100% 50%; /* Primary blue for active item background */
    --sidebar-primary-foreground: 0 0% 100%; /* White text for active item */
    --sidebar-accent: 210 100% 40%; /* Darker blue for hover/active states */
    --sidebar-accent-foreground: 210 20% 98%; /* Light text for accent */
    --sidebar-border: 220 15% 20%; /* Slightly lighter charcoal for sidebar border */
    --sidebar-ring: 210 20% 98%; /* Light ring for focus */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply scroll-smooth antialiased;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-optical-sizing: auto;
    font-feature-settings: 'rlig' 1, 'calt' 1, 'ss01' 1;
    background: linear-gradient(135deg,
      hsl(0, 0%, 92%) 0%,
      hsl(210, 40%, 88%) 25%,
      hsl(0, 0%, 92%) 50%,
      hsl(210, 100%, 95%) 75%,
      hsl(0, 0%, 92%) 100%);
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    letter-spacing: -0.01em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced Typography for Glassmorphism */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  .text-display {
    font-weight: 700;
    letter-spacing: -0.04em;
    line-height: 1.1;
  }

  .text-body {
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  .text-caption {
    font-size: 0.875rem;
    line-height: 1.4;
    letter-spacing: 0.01em;
  }

  /* Modern Dashboard Background */
  .dashboard-background {
    background: linear-gradient(135deg,
      hsl(0, 0%, 92%) 0%,
      hsl(210, 40%, 88%) 25%,
      hsl(0, 0%, 92%) 50%,
      hsl(210, 100%, 95%) 75%,
      hsl(0, 0%, 92%) 100%);
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    min-height: 100vh;
  }

  .dark .dashboard-background {
    background: linear-gradient(135deg,
      hsl(220, 15%, 10%) 0%,
      hsl(220, 15%, 15%) 25%,
      hsl(220, 15%, 10%) 50%,
      hsl(220, 15%, 20%) 75%,
      hsl(220, 15%, 10%) 100%);
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

@layer utilities {
  /* Enhanced Glass Morphism System */
  .glass {
    @apply bg-white/20 dark:bg-black/10 backdrop-blur-xl border border-white/30 dark:border-white/10 shadow-lg;
  }

  .glass-hover {
    @apply hover:bg-white/30 dark:hover:bg-black/20 hover:border-white/40 dark:hover:border-white/20 transition-all duration-300 hover:shadow-xl;
  }

  /* Glass Card Variants */
  .glass-card {
    @apply bg-gradient-to-br from-white/5 to-transparent dark:from-gray-800/15 dark:to-gray-900/5 backdrop-blur-xl border border-white/10 dark:border-gray-600/10 shadow-2xl;
  }

  .glass-card-hover {
    @apply hover:from-white/10 hover:to-white/5 dark:hover:from-gray-700/25 dark:hover:to-gray-800/10 hover:border-white/20 dark:hover:border-gray-500/20 transition-all duration-500 hover:shadow-2xl hover:scale-[1.005];
  }

  /* Sidebar Glassmorphism */
  .glass-sidebar {
    @apply bg-white/10 dark:bg-black/5 backdrop-blur-xl border-r border-white/20 dark:border-white/10 shadow-lg;
  }

  .glass-nav-item {
    @apply bg-transparent hover:bg-white/20 dark:hover:bg-black/20 backdrop-blur-sm border border-transparent hover:border-white/30 dark:hover:border-white/15 transition-all duration-300;
  }

  .glass-sidebar-item-active {
    @apply bg-white/30 dark:bg-black/30 border-white/40 dark:border-white/20;
  }

  /* Advanced Glass Sidebar */
  .glass-sidebar {
    @apply bg-gradient-to-b from-white/15 via-white/10 to-white/5 dark:from-black/20 dark:via-black/15 dark:to-black/10 backdrop-blur-2xl border-r border-white/20 dark:border-white/10 shadow-2xl;
  }

  .glass-sidebar-item {
      @apply bg-transparent hover:bg-white/20 dark:hover:bg-black/20 backdrop-blur-sm border border-transparent hover:border-white/30 dark:hover:border-white/15 transition-all duration-300;
    }

  .glass-sidebar-item-active {
    @apply bg-gradient-to-r from-white/30 to-white/20 dark:from-black/30 dark:to-black/20 border-white/40 dark:border-white/20 shadow-lg;
  }

  /* Glass Navigation */
  .glass-nav {
    @apply bg-white/10 dark:bg-black/10 backdrop-blur-xl border border-white/20 dark:border-white/10 shadow-lg;
  }

  .glass-nav-item {
    @apply hover:bg-white/20 dark:hover:bg-black/20 backdrop-blur-sm transition-all duration-300 border border-transparent hover:border-white/30 dark:hover:border-white/15;
  }

  .glass-nav-item-active {
    @apply bg-white/25 dark:bg-black/25 border-white/40 dark:border-white/20 shadow-md;
  }

  /* Stat Card Glass Effect */
  .glass-stat {
    @apply bg-gradient-to-br backdrop-blur-2xl border border-white/40 dark:border-white/15 shadow-xl;
  }

  .glass-stat-blue {
    @apply from-blue-500/30 to-blue-600/15 dark:from-blue-400/25 dark:to-blue-500/15 hover:from-blue-500/40 hover:to-blue-600/25;
  }

  .glass-stat-amber {
    @apply from-amber-500/30 to-amber-600/15 dark:from-amber-400/25 dark:to-amber-500/15 hover:from-amber-500/40 hover:to-amber-600/25;
  }

  .glass-stat-purple {
    @apply from-purple-500/30 to-purple-600/15 dark:from-purple-400/25 dark:to-purple-500/15 hover:from-purple-500/40 hover:to-purple-600/25;
  }

  .glass-stat-green {
    @apply from-green-500/30 to-green-600/15 dark:from-green-400/25 dark:to-green-500/15 hover:from-green-500/40 hover:to-green-600/25;
  }

  /* Interactive Glass Elements */
  .glass-interactive {
    @apply glass-card glass-card-hover cursor-pointer active:scale-[0.98] active:shadow-lg;
  }

  /* Glass Background Overlays */
  .glass-overlay {
    @apply bg-gradient-to-br from-white/10 to-transparent dark:from-black/10 dark:to-transparent backdrop-blur-sm;
  }

  /* Enhanced Glass Borders for Light Mode */
  .glass-border {
    @apply border border-white/40 dark:border-white/10;
  }

  .glass-border-strong {
    @apply border border-white/60 dark:border-white/20;
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70;
  }

  .text-gradient-blue {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400;
  }

  .text-gradient-purple {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-purple-400;
  }

  /* Mobile-First Glassmorphism Enhancements */
  @media (max-width: 640px) {
    .glass-card {
      @apply backdrop-blur-lg; /* Reduce blur on mobile for better performance */
    }

    .glass-stat {
      @apply backdrop-blur-lg; /* Reduce blur on mobile for better performance */
    }

    /* Improve touch targets on mobile */
    .glass-interactive {
      @apply min-h-[44px] min-w-[44px]; /* Ensure minimum touch target size */
    }

    /* Reduce animations on mobile for better performance */
    .animate-glass-float {
      animation: none;
    }

    /* Optimize glass effects for mobile */
    .glass-card-hover:active {
      @apply scale-[0.95] shadow-lg; /* Provide immediate feedback on touch */
    }
  }

  /* Enhanced touch feedback for mobile */
  @media (hover: none) and (pointer: coarse) {
    .glass-card-hover:hover {
      @apply scale-100 shadow-xl; /* Reset hover effects on touch devices */
    }

    .glass-card-hover:active {
      @apply scale-[0.98] shadow-lg; /* Active state for touch */
    }
  }

  /* Advanced Animation Utilities */
  .animate-stagger-in {
    animation: staggerIn 0.6s ease-out forwards;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 2s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulseGlow 3s ease-in-out infinite;
  }

  .animate-slide-up-fade {
    animation: slideUpFade 0.5s ease-out forwards;
  }

  /* Loading shimmer effect */
  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  /* Micro-interaction animations */
  .hover-lift {
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Enhanced Color Utilities for Glassmorphism */
  .text-glass {
    @apply text-foreground/90;
  }

  .text-glass-muted {
    @apply text-muted-foreground/80;
  }

  .bg-glass-white {
    background: rgba(255, 255, 255, 0.1);
  }

  .bg-glass-black {
    background: rgba(0, 0, 0, 0.1);
  }

  .border-glass {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .dark .border-glass {
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-glass-float,
    .animate-glass-glow,
    .animate-bounce-subtle,
    .animate-pulse-glow {
      animation: none;
    }

    .glass-card-hover {
      transition: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .glass,
    .glass-card,
    .glass-stat {
      @apply bg-background border-2 border-foreground;
      backdrop-filter: none;
    }
  }

  .sidebar {
    @apply bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))] border-r border-[hsl(var(--sidebar-border))] shadow-lg;
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-md transition-colors;
  }

  .sidebar-item-active {
    @apply bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))];
  }

  .sidebar-item-inactive {
    @apply hover:bg-[hsl(var(--sidebar-accent))/20] text-[hsl(var(--sidebar-foreground))];
  }

  .stat-card {
    @apply bg-card rounded-md p-4 shadow-sm border border-border;
  }

  .stat-value {
    @apply text-2xl font-semibold text-card-foreground;
  }

  .stat-label {
    @apply text-sm text-muted-foreground;
  }

  .chart-container {
    @apply bg-card rounded-md p-4 shadow-sm border border-border;
  }

  .filter-dropdown {
    @apply bg-card border border-border rounded-md px-3 py-1.5 text-sm text-card-foreground;
  }
}

/* Compact mode */
.compact-mode .card {
  padding: 0.75rem;
}

.compact-mode .p-4 {
  padding: 0.5rem;
}

.compact-mode .p-6 {
  padding: 0.75rem;
}

.compact-mode .py-4 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.compact-mode .py-6 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.compact-mode .gap-6 {
  gap: 0.75rem;
}

.compact-mode .gap-4 {
  gap: 0.5rem;
}

.compact-mode .space-y-6 > * + * {
  margin-top: 0.75rem;
}

.compact-mode .space-y-4 > * + * {
  margin-top: 0.5rem;
}

.compact-mode .text-lg {
  font-size: 1rem; /* text-base size */
  line-height: 1.5rem; /* text-base line height */
}

.compact-mode .text-xl {
  font-size: 1.125rem; /* text-lg size */
  line-height: 1.75rem; /* text-lg line height */
}

.compact-mode .text-2xl {
  font-size: 1.25rem; /* text-xl size */
  line-height: 1.75rem; /* text-xl line height */
}

.compact-mode .mb-4 {
  margin-bottom: 0.5rem;
}

.compact-mode .mb-6 {
  margin-bottom: 0.75rem;
}

.compact-mode .mt-4 {
  margin-top: 0.5rem;
}

.compact-mode .mt-6 {
  margin-top: 0.75rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Sidebar scrollbar */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-thumb {
  @apply bg-white/30 rounded-full;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  @apply bg-white/50;
}

/* Page transitions */
.page-transition-enter {
  opacity: 0;
  transform: scale(0.98);
}

.page-transition-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: scale(1);
}

.page-transition-exit-active {
  opacity: 0;
  transform: scale(0.98);
  transition: opacity 300ms, transform 300ms;
}

/* Print styles */
@media print {
  body {
    background: white;
    font-size: 12pt;
    color: black;
  }

  .no-print, .no-print * {
    display: none !important;
  }

  .print-container {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    page-break-inside: auto;
  }

  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
  }

  th {
    background-color: #f2f2f2;
    font-weight: bold;
  }

  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }

  img, svg {
    max-width: 100% !important;
  }

  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }

  thead {
    display: table-header-group;
  }

  tfoot {
    display: table-footer-group;
  }
}

/* PDF Export Styles */
.pdf-export-mode {
  background: white;
  padding: 20px;
}

.pdf-export-mode table {
  border-collapse: collapse;
  width: 100%;
  border-spacing: 0;
  margin-bottom: 20px;
}

.pdf-export-mode tr {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  display: table-row;
}

.pdf-export-mode th,
.pdf-export-mode td {
  padding: 8px;
  border: 1px solid #ddd;
  vertical-align: top;
}

.pdf-export-mode th {
  background-color: #f2f2f2;
  font-weight: bold;
  text-align: left;
}

.pdf-export-mode .no-print {
  display: none !important;
}

/* Ensure table headers repeat on each page */
.pdf-export-mode thead {
  display: table-header-group;
}

.pdf-export-mode tfoot {
  display: table-footer-group;
}

/* Add space between rows */
.pdf-export-mode tr.task-row {
  border-bottom: 2px solid #eee;
  margin-bottom: 10px;
  height: auto;
}

/* Ensure proper spacing between cells */
.pdf-export-mode td {
  padding-top: 8px;
  padding-bottom: 8px;
}

/* Ensure proper page breaks */
@media print {
  .pdf-export-mode tr {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  .pdf-export-mode thead {
    display: table-header-group;
  }

  .pdf-export-mode tfoot {
    display: table-footer-group;
  }
}

/* html2pdf specific styles */
.task-row {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

/* Add extra space after each row to prevent content from being cut off */
.task-row td {
  padding-bottom: 12px !important;
}

/* Ensure table headers repeat on each page for html2pdf */
thead {
  display: table-header-group;
}

tfoot {
  display: table-footer-group;
}

/* Add extra padding at the bottom of each page */
@page {
  margin-bottom: 20mm;
}

/* Styles for when PDF is being generated */
.generating-pdf .print-content {
  padding: 30px !important;
}

.generating-pdf table {
  border-collapse: collapse !important;
  width: 100% !important;
}

.generating-pdf tr {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

.generating-pdf td {
  padding: 10px !important;
  vertical-align: top !important;
  border: 1px solid #ddd !important;
}

.generating-pdf th {
  padding: 10px !important;
  background-color: #f2f2f2 !important;
  border: 1px solid #ddd !important;
}


