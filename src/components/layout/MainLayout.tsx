import React, { ReactNode, useState, useEffect, useRef } from 'react';
import Navbar from './Navbar';
import { cn } from '@/lib/utils';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { checkSession } from '@/utils/sessionUtils';
import PermissionBasedNavigation from './PermissionBasedNavigation';
import VerticalSidebar from './VerticalSidebar';
import { useQueryInitialization } from '@/hooks/useQueryInitialization';
import RenderErrorBoundary from '@/components/debug/RenderErrorBoundary';

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { authState, refreshProfile } = useAuth();

  // Initialize query system to handle navigation-based data loading
  useQueryInitialization();

  // Periodically check session validity
  useEffect(() => {
    const verifySession = async () => {
      // Only check if we think we're authenticated
      if (authState?.isAuthenticated) {
        const isValid = await checkSession();

        if (!isValid) {
          console.log('Session invalid in MainLayout, redirecting to login');
          navigate('/login'); // This will be handled by HashRouter as /#/login
        }
      }
    };

    // Check session on mount
    verifySession();

    // Set up interval to check session every 5 minutes
    const interval = setInterval(verifySession, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [authState?.isAuthenticated, navigate]);

  const toggleSidebar = () => {
    setSidebarOpen(prev => !prev);
  };

  // Close sidebar when route changes
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  // Handle window resize and update isMobile state
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (window.innerWidth >= 768) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('mobile-sidebar');
      const toggleButton = document.getElementById('sidebar-toggle');

      if (sidebar &&
          sidebarOpen &&
          !sidebar.contains(event.target as Node) &&
          toggleButton &&
          !toggleButton.contains(event.target as Node)) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarOpen]);

  // Get the page name from the current path
  const getPageTitle = () => {
    const path = location.pathname.split('/')[1];
    if (!path) return 'Dashboard';
    return path.charAt(0).toUpperCase() + path.slice(1);
  };

  // Navigation items are now handled by PermissionBasedNavigation component

  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(() => {
    // Safe initialization that doesn't cause hydration issues
    if (typeof window !== 'undefined') {
      return window.innerWidth < 768;
    }
    return false;
  });

  // Define sidebar width CSS variables
  const SIDEBAR_WIDTH = "16rem";
  const SIDEBAR_WIDTH_COLLAPSED = "4rem";

  const toggleSidebarCollapse = () => {
    setSidebarCollapsed(prev => !prev);
    // Save preference to localStorage
    localStorage.setItem('sidebar-collapsed', (!sidebarCollapsed).toString());
  };

  // Load sidebar collapsed state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-collapsed');
    if (savedState !== null) {
      setSidebarCollapsed(savedState === 'true');
    }
  }, []);

  return (
    <RenderErrorBoundary>
      <div
        className="min-h-screen"
        style={{
          '--sidebar-width': sidebarCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH
        } as React.CSSProperties}
      >
      {/* Desktop Vertical Sidebar */}
      <div className="hidden md:block">
        <VerticalSidebar collapsed={sidebarCollapsed} onToggleCollapse={toggleSidebarCollapse} />
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-20 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div
        id="mobile-sidebar"
        className={cn(
          "fixed inset-y-0 left-0 z-30 w-64 sidebar transform transition-transform duration-200 ease-in-out md:hidden overflow-y-auto",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="p-4 border-b border-blue-600">
          <div className="flex items-center justify-center">
            <img
              src="/icons/logo.png"
              alt="StayFu Logo"
              className="h-8 w-8 object-contain bg-white rounded-full p-1"
            />
            <span className="ml-3 text-lg font-semibold text-white">StayFu</span>
          </div>
        </div>
        <PermissionBasedNavigation
          mobile={true}
          onItemClick={() => setSidebarOpen(false)}
        />
      </div>

      <div className="flex flex-col flex-grow transition-all duration-200"
        style={{
          marginLeft: !isMobile ? (sidebarCollapsed ? '4rem' : '16rem') : '0',
          width: !isMobile ? `calc(100% - ${sidebarCollapsed ? '4rem' : '16rem'})` : '100%'
        }}>
        {/* Mobile only navbar */}
        <div className="md:hidden">
          <Navbar toggleSidebar={toggleSidebar} />
        </div>

        <main className="flex-grow pb-4 w-full overflow-x-hidden px-4 pt-2 pb-4 max-w-full glass-card glass-card-hover m-4 transition-all duration-300">
          <h1 className="sr-only">{getPageTitle()}</h1>
          {children}
        </main>
      </div>
    </div>
    </RenderErrorBoundary>
  );
};

export default MainLayout;
