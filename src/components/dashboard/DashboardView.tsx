import React, { memo, useMemo, useEffect, useState } from 'react';
import {
  Building2,
  Wrench,
  Package,
  AlertCircle,
  ArrowRight,
  Calendar,
  Circle,
  ShoppingCart,
  BarChart3,
  InfoIcon,
  PieChart,
  Activity,
  CalendarCheck,
  ListTodo,
  PackageSearch,
  ClipboardList,
  ChevronDown,
  ChevronRight,
  AlertTriangle
} from 'lucide-react';
import { Property } from '../properties/PropertyCard';
import { MaintenanceTask } from '../maintenance/types';
import { InventoryItem } from '../inventory/types';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { PurchaseOrder } from '@/types/inventory';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import StatCard from "@/components/ui/StatCard";

interface Damage {
  id: string;
  title: string;
  propertyName: string;
  status: 'new' | 'pending' | 'completed';
  reportedAt: string; // Assuming this is a formatted string like "Apr 15" or similar
}

interface DashboardViewProps {
  properties: Property[];
  maintenanceTasks: MaintenanceTask[];
  inventoryItems: InventoryItem[];
  damages: Damage[];
  purchaseOrders?: PurchaseOrder[];
  isLoading?: boolean;
  onViewMore: (section: 'properties' | 'maintenance' | 'inventory' | 'damages' | 'purchaseOrders') => void;
  onViewOrder?: (order: PurchaseOrder) => void;
}

// Helper function to calculate days until a date string (handles date ranges like "Apr 24 - Apr 27, 2025")
function calculateDaysUntil(dateStr: string | null | undefined): number {
  if (!dateStr) return Infinity;
  try {
    console.log(`[calculateDaysUntil] Calculating days until: ${dateStr}`);

    // Special case for YYYY-MM-DD format (next_checkin_date)
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      console.log(`[calculateDaysUntil] Detected ISO date format: ${dateStr}`);

      // Create date objects using the date parts to avoid timezone issues
      const [year, month, day] = dateStr.split('-').map(Number);
      const targetDate = new Date(year, month - 1, day); // month is 0-indexed in JS Date

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Check if the date is valid
      if (isNaN(targetDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid ISO date: ${dateStr}`);
        return Infinity;
      }

      const diffTime = targetDate.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${targetDate.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${dateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${dateStr}: ${diffDays}`);
      return diffDays;
    }
    // Check if the date string is a range (contains a hyphen)
    else if (dateStr.includes('-')) {
      console.log(`[calculateDaysUntil] Detected date range: ${dateStr}`);

      // Extract the start date from the range (e.g., "Apr 24" from "Apr 24 - Apr 27, 2025")
      const parts = dateStr.split('-');
      let startDateStr = parts[0].trim();

      // If the year is only at the end of the range, add it to the start date
      if (!startDateStr.includes(',') && parts[1].includes(',')) {
        const year = parts[1].trim().split(',')[1].trim();
        startDateStr = `${startDateStr}, ${year}`;
      }

      console.log(`[calculateDaysUntil] Extracted start date: ${startDateStr}`);

      // Parse the start date
      const startDate = new Date(startDateStr);

      // Check if the date is valid
      if (isNaN(startDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid start date: ${startDateStr}`);

        // Try using next_checkin_date if available
        if (typeof window !== 'undefined') {
          const properties = JSON.parse(localStorage.getItem('properties') || '[]');
          const property = properties.find((p: any) => p.next_booking === dateStr);
          if (property && property.next_checkin_date) {
            console.log(`[calculateDaysUntil] Using next_checkin_date instead: ${property.next_checkin_date}`);
            return calculateDaysUntil(property.next_checkin_date);
          }
        }

        return Infinity;
      }

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Normalize the start date to midnight in local timezone
      const startYear = startDate.getFullYear();
      const startMonth = startDate.getMonth();
      const startDay = startDate.getDate();
      const startDateNormalized = new Date(startYear, startMonth, startDay);

      const diffTime = startDateNormalized.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${startDateNormalized.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${startDateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${startDateStr}: ${diffDays}`);
      return diffDays;
    } else {
      // Handle single date (not a range)
      // If the date string doesn't include time, add T00:00:00 to ensure consistent parsing
      const normalizedDateStr = dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00`;
      const targetDate = new Date(normalizedDateStr);

      // Check if the date is valid
      if (isNaN(targetDate.getTime())) {
        console.error(`[calculateDaysUntil] Invalid date: ${dateStr}`);
        return Infinity;
      }

      // Create today's date at midnight in local timezone
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDay = today.getDate();
      const todayNormalized = new Date(todayYear, todayMonth, todayDay);

      // Normalize the target date to midnight in local timezone
      const targetYear = targetDate.getFullYear();
      const targetMonth = targetDate.getMonth();
      const targetDay = targetDate.getDate();
      const targetDateNormalized = new Date(targetYear, targetMonth, targetDay);

      const diffTime = targetDateNormalized.getTime() - todayNormalized.getTime();
      console.log(`[calculateDaysUntil] Today: ${todayNormalized.toISOString()}, Target: ${targetDateNormalized.toISOString()}, Diff ms: ${diffTime}`);

      if (diffTime < 0) {
        console.log(`[calculateDaysUntil] Date ${dateStr} is in the past`);
        return Infinity; // Past dates are not "upcoming"
      }

      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      console.log(`[calculateDaysUntil] Days until ${dateStr}: ${diffDays}`);
      return diffDays;
    }
  } catch (e) {
    console.error("Error parsing date:", dateStr, e);
    return Infinity;
  }
}

// Helper function to format date (handles date ranges)
const formatDate = (dateStr: string | null | undefined): string => {
  if (!dateStr) return 'N/A';
  try {
    console.log(`[formatDate] Formatting date: ${dateStr}`);

    // If it's a date range (contains a hyphen), return it as is
    if (dateStr.includes('-')) {
      console.log(`[formatDate] Date range detected, returning as is: ${dateStr}`);
      return dateStr;
    }

    // For single dates
    // If the date string doesn't include time, add T00:00:00 to ensure consistent parsing
    const normalizedDateStr = dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00`;
    const date = new Date(normalizedDateStr);

    if (isNaN(date.getTime())) {
      console.error(`[formatDate] Invalid date: ${dateStr}`);
      // Return the original string if we can't parse it
      return dateStr;
    }

    const formatted = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    console.log(`[formatDate] Formatted ${dateStr} as: ${formatted}`);
    return formatted;
  } catch (e) {
    console.error("Error formatting date:", dateStr, e);
    // Return the original string if we encounter an error
    return dateStr;
  }
};

 // Memoize the component to prevent unnecessary re-renders
 const DashboardView: React.FC<DashboardViewProps> = ({
  properties = [], // Default to empty array
  maintenanceTasks = [], // Default to empty array
  inventoryItems = [], // Default to empty array
  damages = [], // Default to empty array
  purchaseOrders = [],
  isLoading = false,
  onViewMore,
  onViewOrder,
}) => {
  const navigate = useNavigate();

  // Accordion state management
  const [expandedSections, setExpandedSections] = useState<{
    properties: boolean;
    maintenance: boolean;
    criticalTasks: boolean;
    lowStock: boolean;
    newDamages: boolean;
    pendingOrders: boolean;
    checkins: boolean;
  }>({
    properties: false,
    maintenance: false,
    criticalTasks: false,
    lowStock: false,
    newDamages: false,
    pendingOrders: false,
    checkins: false,
  });

  // Toggle accordion section
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Log maintenance tasks when they change
  useEffect(() => {
    console.log('[DashboardView] Maintenance tasks received:', maintenanceTasks);
  }, [maintenanceTasks]);

  // Filter critical tasks (exclude completed and cancelled)
  const criticalTasks = useMemo(() => isLoading ? [] : maintenanceTasks.filter(
    (task) => task && 
    (task.severity === 'critical' || task.severity === 'high') &&
    task.status !== 'completed' && 
    task.status !== 'cancelled'
  ), [maintenanceTasks, isLoading]);

  // Filter low stock items
  const lowStockItems = useMemo(() => isLoading ? [] : inventoryItems.filter(
    (item) => item && typeof item.quantity === 'number' && typeof item.minQuantity === 'number' && item.quantity <= item.minQuantity
  ), [inventoryItems, isLoading]);

  // Filter new damage reports
  const newDamages = useMemo(() => isLoading ? [] : damages.filter((damage) => damage && damage.status === 'new'), [damages, isLoading]);

  // Filter pending purchase orders
  const pendingOrders = useMemo(() => isLoading ? [] : purchaseOrders.filter(
    (order) => order && order.status === 'pending'
  ), [purchaseOrders, isLoading]);

  // Filter upcoming check-ins (within next 7 days, sorted)
  const upcomingCheckins = useMemo(() => {
    if (isLoading) return [];

    console.log('[DashboardView] Processing properties for check-ins:', properties.length);
    console.log('[DashboardView] All properties:', JSON.stringify(properties, null, 2));

    // Log all properties with next_booking or next_checkin_date for debugging
    properties.forEach(p => {
      if (p.next_booking) {
        console.log(`[DashboardView] Property ${p.name} has next_booking: ${p.next_booking}`);
      }
      if (p.next_checkin_date) {
        console.log(`[DashboardView] Property ${p.name} has next_checkin_date: ${p.next_checkin_date}`);
      }
      if (p.next_checkin_formatted) {
        console.log(`[DashboardView] Property ${p.name} has next_checkin_formatted: ${p.next_checkin_formatted}`);
      }
    });

    // Store properties in localStorage for debugging
    if (typeof window !== 'undefined') {
      localStorage.setItem('properties', JSON.stringify(properties));
    }

    // First, create a copy of the properties array to avoid mutation issues
    const propertiesWithDays = properties
      .filter(p => {
        // Include properties that have either next_booking or next_checkin_date
        const hasNextBooking = !!p.next_booking;
        const hasNextCheckinDate = !!p.next_checkin_date;
        console.log(`[DashboardView] Property ${p.name}: has next booking = ${hasNextBooking}, has next checkin date = ${hasNextCheckinDate}`);
        return p && (hasNextBooking || hasNextCheckinDate);
      })
      .map(p => {
        // Try to calculate days using next_checkin_date first if available, then fall back to next_booking
        let daysUntil;
        if (p.next_checkin_date) {
          daysUntil = calculateDaysUntil(p.next_checkin_date);
          console.log(`[DashboardView] Property ${p.name}: using next_checkin_date, days until: ${daysUntil}`);
        } else if (p.next_booking) {
          daysUntil = calculateDaysUntil(p.next_booking);
          console.log(`[DashboardView] Property ${p.name}: using next_booking, days until: ${daysUntil}`);
        } else {
          daysUntil = Infinity;
          console.log(`[DashboardView] Property ${p.name}: no date available, setting days until to Infinity`);
        }

        return { ...p, daysUntil };
      });

    console.log('[DashboardView] Properties with days calculated:', propertiesWithDays.map(p => ({ name: p.name, daysUntil: p.daysUntil })));

    const upcomingProperties = propertiesWithDays
      .filter(p => {
        const isUpcoming = p.daysUntil <= 7 && p.daysUntil !== Infinity;
        console.log(`[DashboardView] Property ${p.name}: is upcoming (≤7 days) = ${isUpcoming}, days until: ${p.daysUntil}`);
        return isUpcoming; // Show check-ins within the next 7 days
      })
      .sort((a, b) => a.daysUntil - b.daysUntil);

    console.log('[DashboardView] Final upcoming properties:', upcomingProperties.map(p => p.name));
    return upcomingProperties;
  }, [properties, isLoading]);


  // Calculate counts for stat cards
  const propertiesCount = isLoading ? '-' : properties.length;
  const criticalTasksCount = isLoading ? '-' : criticalTasks.length;
  const lowStockItemsCount = isLoading ? '-' : lowStockItems.length;
  const pendingOrdersCount = isLoading ? '-' : pendingOrders.length;

   // Calculate specific counts for Property Overview
   const occupiedCount = useMemo(() => isLoading ? '-' : properties.filter(p => p && p.is_occupied).length, [properties, isLoading]);
   const vacantCount = useMemo(() => isLoading ? '-' : properties.filter(p => p && !p.is_occupied).length, [properties, isLoading]);
   const nextCheckin = useMemo(() => isLoading || upcomingCheckins.length === 0 ? null : upcomingCheckins[0], [upcomingCheckins, isLoading]);


    // Event handlers
    const handlePropertyClick = (id: string) => navigate(`/properties/${id}`);
  const handleTaskClick = (id: string) => navigate(`/maintenance?id=${id}`);
  const handleInventoryClick = (id: string) => navigate(`/inventory?itemId=${id}`);
  const handleDamageClick = (id: string) => navigate(`/damages/${id}`);
  const handleOrderClick = (order: PurchaseOrder) => {
    if (onViewOrder) {
      onViewOrder(order);
    } else {
      navigate(`/purchase-orders/${order.id}`);
    }
  };

  // Stat cards configuration with enhanced glassmorphism design
  const statCards = useMemo(() => [
    {
      title: 'Properties',
      value: propertiesCount,
      icon: Building2,
      colorScheme: 'blue' as const,
      onClick: () => onViewMore('properties'),
      alert: false,
      subtitle: `${occupiedCount} occupied, ${vacantCount} vacant`
    },
    {
      title: 'Critical Tasks',
      value: criticalTasksCount,
      icon: Wrench,
      colorScheme: 'amber' as const,
      onClick: () => onViewMore('maintenance'),
      alert: typeof criticalTasksCount === 'number' && criticalTasksCount > 0,
      subtitle: 'Requires immediate attention'
    },
    {
      title: 'Low Stock Items',
      value: lowStockItemsCount,
      icon: Package,
      colorScheme: 'purple' as const,
      onClick: () => onViewMore('inventory'),
      alert: typeof lowStockItemsCount === 'number' && lowStockItemsCount > 0,
      subtitle: 'Need restocking'
    },
    {
      title: 'Pending Orders',
      value: pendingOrdersCount,
      icon: ShoppingCart,
      colorScheme: 'green' as const,
      onClick: () => onViewMore('purchaseOrders'),
      alert: false,
      subtitle: 'Awaiting processing'
    },
  ], [propertiesCount, criticalTasksCount, lowStockItemsCount, pendingOrdersCount, occupiedCount, vacantCount, onViewMore]);


  // Render Loading Skeleton for Cards
  const CardSkeleton = () => (
    <div className="animate-pulse h-48 bg-muted/50 rounded-md" />
  );

  // Render Empty State for Lists
  const EmptyListState = ({ icon: Icon, message }: { icon: React.ElementType, message: string }) => (
    <div className="text-center py-6 text-muted-foreground">
      <Icon className="w-6 h-6 mx-auto mb-2 opacity-40" />
      <p className="text-sm">{message}</p>
    </div>
  );

  return (
    <div className="w-full overflow-x-hidden space-y-6">
      {/* Enhanced Stat Cards with Glassmorphism */}
      <div className="glass-card glass-card-hover p-6 rounded-xl">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            Overview
          </h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
          {statCards.map((stat, index) => (
            <StatCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={stat.icon}
              colorScheme={stat.colorScheme}
              alert={stat.alert}
              loading={isLoading}
              subtitle={stat.subtitle}
              onClick={stat.onClick}
              className="animate-fade-in hover-lift"
              style={{ animationDelay: `${index * 50}ms` }}
            />
          ))}
        </div>
      </div>

      {/* Enhanced Main Content Grid - Mobile Responsive */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 w-full">

        {/* Left Column (Overview Cards) - Mobile Optimized */}
        <div className="lg:col-span-2 space-y-6 w-full min-w-0">
          {/* Enhanced Properties Overview */}
          <Card className="glass-card glass-card-hover border-0 shadow-xl rounded-xl">
            <CardHeader className="py-4 px-6">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => toggleSection('properties')}
                  className="flex items-center gap-2 text-sm hover:bg-white/20 dark:hover:bg-black/20 rounded-lg p-2 -m-2 transition-all duration-200 text-foreground group"
                >
                  <ChevronRight className={`h-4 w-4 text-blue-600 dark:text-blue-400 transition-transform duration-200 ${expandedSections.properties ? 'rotate-90' : ''}`} />
                  <Building2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="font-semibold text-foreground group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Properties ({properties.length})</span>
                </button>
                <button
                  onClick={() => onViewMore('properties')}
                  className="text-sm text-primary dark:text-primary-foreground hover:underline flex items-center font-medium glass-interactive px-3 py-1 rounded-lg"
                >
                  View All <ArrowRight size={12} className="ml-1" />
                </button>
              </div>
            </CardHeader>
            {/* Always show summary, expand to show details */}
            <CardContent className="p-6">
              {/* Summary Row - Always Visible */}
              <div className="flex gap-4 mb-4">
                <div className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-green-50/80 to-green-100/60 dark:from-green-950/50 dark:to-green-900/30 border border-green-200/50 dark:border-green-800/30 flex-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-semibold text-green-700 dark:text-green-400">
                    {occupiedCount} Occupied
                  </span>
                </div>
                <div className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-red-50/80 to-red-100/60 dark:from-red-950/50 dark:to-red-900/30 border border-red-200/50 dark:border-red-800/30 flex-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-semibold text-red-700 dark:text-red-400">
                    {vacantCount} Vacant
                  </span>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedSections.properties && (
                <div className="space-y-4 border-t border-white/20 dark:border-white/10 pt-4 animate-in slide-in-from-top-2 duration-200">
                  {isLoading ? (
                    <div className="space-y-2">
                      <div className="h-6 bg-muted animate-pulse rounded-lg"></div>
                      <div className="h-6 bg-muted animate-pulse rounded-lg"></div>
                    </div>
                  ) : properties.length > 0 ? (
                    <>
                      {/* Property List */}
                      <div>
                        <div className="flex items-center gap-2 mb-3">
                          <Building2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          <span className="text-sm font-semibold text-muted-foreground">All Properties</span>
                        </div>
                        <div className="space-y-2 max-h-40 overflow-y-auto overflow-x-hidden">
                          {properties.slice(0, 10).map(property => (
                            <div
                              key={property.id}
                              className="flex justify-between items-center text-sm hover:bg-white/20 dark:hover:bg-black/20 p-3 rounded-lg cursor-pointer min-w-0 transition-all duration-200 group"
                              onClick={() => handlePropertyClick(property.id)}
                            >
                              <span className="font-medium truncate flex-1 min-w-0 group-hover:text-blue-600 dark:group-hover:text-blue-400">{property.name}</span>
                              <div className="flex items-center gap-2 flex-shrink-0">
                                <div className={`w-2.5 h-2.5 rounded-full ${property.is_occupied ? 'bg-green-500' : 'bg-red-500'} shadow-sm`}></div>
                                <span className={`text-sm font-medium ${property.is_occupied ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                  {property.is_occupied ? 'Occupied' : 'Vacant'}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Upcoming Check-ins */}
                      {upcomingCheckins.length > 0 && (
                        <div className="border-t border-white/20 dark:border-white/10 pt-4">
                          <div className="flex items-center gap-2 mb-3">
                            <CalendarCheck className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                            <span className="text-sm font-semibold text-muted-foreground">Upcoming Check-ins</span>
                          </div>
                          <div className="space-y-2 max-h-32 overflow-y-auto overflow-x-hidden">
                            {upcomingCheckins.map(property => (
                              <div
                                key={property.id}
                                className="flex justify-between items-center text-sm hover:bg-white/20 dark:hover:bg-black/20 p-3 rounded-lg cursor-pointer min-w-0 transition-all duration-200 group"
                                onClick={() => handlePropertyClick(property.id)}
                              >
                                <span className="font-medium truncate flex-1 min-w-0 group-hover:text-teal-600 dark:group-hover:text-teal-400">{property.name}</span>
                                <span className="text-teal-600 dark:text-teal-400 text-sm font-medium ml-2 flex-shrink-0">
                                  {property.daysUntil === 0 ? 'Today' :
                                   property.daysUntil === 1 ? 'Tomorrow' :
                                   `${property.daysUntil}d`}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-6">
                      <Building2 className="w-8 h-8 mx-auto mb-2 opacity-40" />
                      <p className="text-sm text-muted-foreground">No properties</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Enhanced Maintenance Overview */}
          <Card className="glass-card glass-card-hover border-0 shadow-xl rounded-xl">
            <CardHeader className="py-4 px-6">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => toggleSection('maintenance')}
                  className="flex items-center gap-2 text-sm hover:bg-white/20 dark:hover:bg-black/20 rounded-lg p-2 -m-2 transition-all duration-200 text-foreground group"
                >
                  <ChevronRight className={`h-4 w-4 text-amber-600 dark:text-amber-400 transition-transform duration-200 ${expandedSections.maintenance ? 'rotate-90' : ''}`} />
                  <Wrench className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <span className="font-semibold text-foreground group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors">Maintenance ({maintenanceTasks.length})</span>
                </button>
                <button
                  onClick={() => onViewMore('maintenance')}
                  className="text-sm text-primary dark:text-primary-foreground hover:underline flex items-center font-medium glass-interactive px-3 py-1 rounded-lg"
                >
                  View All <ArrowRight size={12} className="ml-1" />
                </button>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              {/* Summary Row - Always Visible */}
              <div className="flex gap-4 mb-4">
                <div
                  className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-red-50/80 to-red-100/60 dark:from-red-950/50 dark:to-red-900/30 border border-red-200/50 dark:border-red-800/30 flex-1 cursor-pointer hover:from-red-100 hover:to-red-200/80 dark:hover:from-red-900/70 dark:hover:to-red-800/50 transition-all duration-200"
                  onClick={() => {
                    window.sessionStorage.setItem('maintenance_severity_filter', 'critical');
                    navigate('/maintenance');
                  }}
                >
                  <div className="w-3 h-3 bg-red-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-semibold text-red-700 dark:text-red-400">
                    {maintenanceTasks.filter(task => task.severity === 'critical' && task.status !== 'completed' && task.status !== 'cancelled').length} Critical
                  </span>
                </div>
                <div
                  className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-amber-50/80 to-amber-100/60 dark:from-amber-950/50 dark:to-amber-900/30 border border-amber-200/50 dark:border-amber-800/30 flex-1 cursor-pointer hover:from-amber-100 hover:to-amber-200/80 dark:hover:from-amber-900/70 dark:hover:to-amber-800/50 transition-all duration-200"
                  onClick={() => {
                    window.sessionStorage.setItem('maintenance_severity_filter', 'high');
                    navigate('/maintenance');
                  }}
                >
                  <div className="w-3 h-3 bg-amber-500 rounded-full shadow-sm"></div>
                  <span className="text-sm font-semibold text-amber-700 dark:text-amber-400">
                    {maintenanceTasks.filter(task => task.severity === 'high' && task.status !== 'completed' && task.status !== 'cancelled').length} High
                  </span>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedSections.maintenance && (
                <div className="space-y-4 border-t border-white/20 dark:border-white/10 pt-4 animate-in slide-in-from-top-2 duration-200">
                  {isLoading ? (
                    <div className="space-y-2">
                      <div className="h-6 bg-muted animate-pulse rounded-lg"></div>
                      <div className="h-6 bg-muted animate-pulse rounded-lg"></div>
                    </div>
                  ) : maintenanceTasks && maintenanceTasks.length > 0 ? (
                    <>
                      {/* Critical Tasks */}
                      {maintenanceTasks.filter(task => task.severity === 'critical' && task.status !== 'completed' && task.status !== 'cancelled').length > 0 && (
                        <div>
                          <div className="flex items-center gap-2 mb-3">
                            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                            <span className="text-sm font-semibold text-muted-foreground">Critical Tasks</span>
                          </div>
                          <div className="space-y-2 max-h-32 overflow-y-auto">
                            {maintenanceTasks.filter(task => task.severity === 'critical' && task.status !== 'completed' && task.status !== 'cancelled').slice(0, 5).map(task => (
                              <div
                                key={task.id}
                                className="flex justify-between items-center text-sm hover:bg-white/20 dark:hover:bg-black/20 p-3 rounded-lg cursor-pointer transition-all duration-200 group"
                                onClick={() => navigate(`/maintenance?id=${task.id}`)}
                              >
                                <span className="font-medium truncate flex-1 group-hover:text-red-600 dark:group-hover:text-red-400">{task.title}</span>
                                <span className="text-red-600 dark:text-red-400 text-sm font-medium ml-2">{task.status}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* High Priority Tasks */}
                      {maintenanceTasks.filter(task => task.severity === 'high' && task.status !== 'completed' && task.status !== 'cancelled').length > 0 && (
                        <div className="border-t border-white/20 dark:border-white/10 pt-4">
                          <div className="flex items-center gap-2 mb-3">
                            <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                            <span className="text-sm font-semibold text-muted-foreground">High Priority Tasks</span>
                          </div>
                          <div className="space-y-2 max-h-32 overflow-y-auto">
                            {maintenanceTasks.filter(task => task.severity === 'high' && task.status !== 'completed' && task.status !== 'cancelled').slice(0, 5).map(task => (
                              <div
                                key={task.id}
                                className="flex justify-between items-center text-sm hover:bg-white/20 dark:hover:bg-black/20 p-3 rounded-lg cursor-pointer transition-all duration-200 group"
                                onClick={() => navigate(`/maintenance?id=${task.id}`)}
                              >
                                <span className="font-medium truncate flex-1 group-hover:text-amber-600 dark:group-hover:text-amber-400">{task.title}</span>
                                <span className="text-amber-600 dark:text-amber-400 text-sm font-medium ml-2">{task.status}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Status Summary */}
                      <div className="border-t border-white/20 dark:border-white/10 pt-4">
                        <div className="flex gap-3 text-sm">
                          <div
                            className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gradient-to-r from-blue-50/80 to-blue-100/60 dark:from-blue-950/50 dark:to-blue-900/30 border border-blue-200/50 dark:border-blue-800/30 cursor-pointer hover:from-blue-100 hover:to-blue-200/80 dark:hover:from-blue-900/70 dark:hover:to-blue-800/50 transition-all duration-200"
                            onClick={() => {
                              window.sessionStorage.setItem('maintenance_status_filter', 'new');
                              navigate('/maintenance');
                            }}
                          >
                            <span className="text-blue-700 dark:text-blue-400 font-medium">New:</span>
                            <span className="font-bold text-blue-700 dark:text-blue-400">{maintenanceTasks.filter(task => task.status === 'new').length}</span>
                          </div>
                          <div
                            className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gradient-to-r from-purple-50/80 to-purple-100/60 dark:from-purple-950/50 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 cursor-pointer hover:from-purple-100 hover:to-purple-200/80 dark:hover:from-purple-900/70 dark:hover:to-purple-800/50 transition-all duration-200"
                            onClick={() => {
                              window.sessionStorage.setItem('maintenance_status_filter', 'in_progress');
                              navigate('/maintenance');
                            }}
                          >
                            <span className="text-purple-700 dark:text-purple-400 font-medium">Active:</span>
                            <span className="font-bold text-purple-700 dark:text-purple-400">{maintenanceTasks.filter(task => task.status === 'in_progress' || task.status === 'assigned').length}</span>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-6">
                      <Wrench className="w-8 h-8 mx-auto mb-2 opacity-40" />
                      <p className="text-sm text-muted-foreground">No maintenance tasks</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column (Enhanced Action Items) - Mobile Optimized */}
        <div className="lg:col-span-1 w-full min-w-0">
          <Card className="glass-card glass-card-hover border-0 shadow-xl rounded-xl">
            <CardHeader className="py-4 px-6">
              <CardTitle className="text-lg flex items-center gap-2 text-foreground font-semibold">
                <Activity className="h-5 w-5 text-primary" />
                Action Items
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Critical Tasks Accordion */}
                {criticalTasks.length > 0 && (
                  <div className="glass-card rounded-lg bg-gradient-to-r from-red-50/80 to-red-100/60 dark:from-red-950/50 dark:to-red-900/30 border border-red-200/50 dark:border-red-800/30">
                    <button
                      onClick={() => toggleSection('criticalTasks')}
                      className="w-full flex items-center justify-between p-4 hover:bg-red-100/50 dark:hover:bg-red-900/30 transition-all duration-200 text-foreground"
                    >
                      <div className="flex items-center gap-3">
                        <ChevronRight className={`h-4 w-4 text-red-600 dark:text-red-400 transition-transform duration-200 ${expandedSections.criticalTasks ? 'rotate-90' : ''}`} />
                        <Wrench className="h-4 w-4 text-red-600 dark:text-red-400" />
                        <span className="text-sm font-semibold text-red-700 dark:text-red-300">Critical Tasks</span>
                      </div>
                      <span className="text-sm font-bold text-red-700 dark:text-red-300 bg-red-200/50 dark:bg-red-800/30 px-2 py-1 rounded-full">{criticalTasks.length}</span>
                    </button>
                    {expandedSections.criticalTasks && (
                      <div className="border-t border-red-200/50 dark:border-red-800/30 p-4 space-y-2 max-h-40 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {criticalTasks.slice(0, 10).map(task => (
                          <div
                            key={task.id}
                            className="flex justify-between items-center text-sm hover:bg-red-100/50 dark:hover:bg-red-900/30 p-3 rounded-lg cursor-pointer min-w-0 transition-all duration-200 group"
                            onClick={() => navigate(`/maintenance?id=${task.id}`)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0 group-hover:text-red-600 dark:group-hover:text-red-400">{task.title}</span>
                            <span className="text-red-600 dark:text-red-400 text-sm font-medium ml-2 flex-shrink-0">{task.status}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Low Stock Accordion */}
                {lowStockItems.length > 0 && (
                  <div className="glass-card rounded-lg bg-gradient-to-r from-purple-50/80 to-purple-100/60 dark:from-purple-950/50 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30">
                    <button
                      onClick={() => toggleSection('lowStock')}
                      className="w-full flex items-center justify-between p-4 hover:bg-purple-100/50 dark:hover:bg-purple-900/30 transition-all duration-200 text-foreground"
                    >
                      <div className="flex items-center gap-3">
                        <ChevronRight className={`h-4 w-4 text-purple-600 dark:text-purple-400 transition-transform duration-200 ${expandedSections.lowStock ? 'rotate-90' : ''}`} />
                        <Package className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                        <span className="text-sm font-semibold text-purple-700 dark:text-purple-300">Low Stock</span>
                      </div>
                      <span className="text-sm font-bold text-purple-700 dark:text-purple-300 bg-purple-200/50 dark:bg-purple-800/30 px-2 py-1 rounded-full">{lowStockItems.length}</span>
                    </button>
                    {expandedSections.lowStock && (
                      <div className="border-t border-purple-200/50 dark:border-purple-800/30 p-4 space-y-2 max-h-40 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {lowStockItems.slice(0, 10).map(item => (
                          <div
                            key={item.id}
                            className="flex justify-between items-center text-sm hover:bg-purple-100/50 dark:hover:bg-purple-900/30 p-3 rounded-lg cursor-pointer min-w-0 transition-all duration-200 group"
                            onClick={() => navigate(`/inventory?itemId=${item.id}`)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0 group-hover:text-purple-600 dark:group-hover:text-purple-400">{item.name}</span>
                            <span className="text-purple-600 dark:text-purple-400 text-sm font-medium ml-2 flex-shrink-0">{item.quantity}/{item.minQuantity}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* New Damages Accordion */}
                {newDamages.length > 0 && (
                  <div className="glass-card rounded-lg bg-gradient-to-r from-blue-50/80 to-blue-100/60 dark:from-blue-950/50 dark:to-blue-900/30 border border-blue-200/50 dark:border-blue-800/30">
                    <button
                      onClick={() => toggleSection('newDamages')}
                      className="w-full flex items-center justify-between p-4 hover:bg-blue-100/50 dark:hover:bg-blue-900/30 transition-all duration-200 text-foreground"
                    >
                      <div className="flex items-center gap-3">
                        <ChevronRight className={`h-4 w-4 text-blue-600 dark:text-blue-400 transition-transform duration-200 ${expandedSections.newDamages ? 'rotate-90' : ''}`} />
                        <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-sm font-semibold text-blue-700 dark:text-blue-300">New Damages</span>
                      </div>
                      <span className="text-sm font-bold text-blue-700 dark:text-blue-300 bg-blue-200/50 dark:bg-blue-800/30 px-2 py-1 rounded-full">{newDamages.length}</span>
                    </button>
                    {expandedSections.newDamages && (
                      <div className="border-t border-blue-200/50 dark:border-blue-800/30 p-4 space-y-2 max-h-40 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {newDamages.slice(0, 10).map(damage => (
                          <div
                            key={damage.id}
                            className="flex justify-between items-center text-sm hover:bg-blue-100/50 dark:hover:bg-blue-900/30 p-3 rounded-lg cursor-pointer min-w-0 transition-all duration-200 group"
                            onClick={() => navigate(`/damages/${damage.id}`)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0 group-hover:text-blue-600 dark:group-hover:text-blue-400">{damage.title}</span>
                            <span className="text-blue-600 dark:text-blue-400 text-sm font-medium ml-2 flex-shrink-0">{damage.status}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Pending Orders Accordion */}
                {pendingOrders.length > 0 && (
                  <div className="glass-card rounded-lg bg-gradient-to-r from-green-50/80 to-green-100/60 dark:from-green-950/50 dark:to-green-900/30 border border-green-200/50 dark:border-green-800/30">
                    <button
                      onClick={() => toggleSection('pendingOrders')}
                      className="w-full flex items-center justify-between p-4 hover:bg-green-100/50 dark:hover:bg-green-900/30 transition-all duration-200 text-foreground"
                    >
                      <div className="flex items-center gap-3">
                        <ChevronRight className={`h-4 w-4 text-green-600 dark:text-green-400 transition-transform duration-200 ${expandedSections.pendingOrders ? 'rotate-90' : ''}`} />
                        <ShoppingCart className="h-4 w-4 text-green-600 dark:text-green-400" />
                        <span className="text-sm font-semibold text-green-700 dark:text-green-300">Pending Orders</span>
                      </div>
                      <span className="text-sm font-bold text-green-700 dark:text-green-300 bg-green-200/50 dark:bg-green-800/30 px-2 py-1 rounded-full">{pendingOrders.length}</span>
                    </button>
                    {expandedSections.pendingOrders && (
                      <div className="border-t border-green-200/50 dark:border-green-800/30 p-4 space-y-2 max-h-40 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {pendingOrders.slice(0, 10).map(order => (
                          <div
                            key={order.id}
                            className="flex justify-between items-center text-sm hover:bg-green-100/50 dark:hover:bg-green-900/30 p-3 rounded-lg cursor-pointer min-w-0 transition-all duration-200 group"
                            onClick={() => handleOrderClick(order)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0 group-hover:text-green-600 dark:group-hover:text-green-400">Order #{order.id.substring(0, 6)}</span>
                            <span className="text-green-600 dark:text-green-400 text-sm font-medium ml-2 flex-shrink-0">{order.status}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Upcoming Check-ins Accordion */}
                {upcomingCheckins.length > 0 && (
                  <div className="glass-card rounded-lg bg-gradient-to-r from-teal-50/80 to-teal-100/60 dark:from-teal-950/50 dark:to-teal-900/30 border border-teal-200/50 dark:border-teal-800/30">
                    <button
                      onClick={() => toggleSection('checkins')}
                      className="w-full flex items-center justify-between p-4 hover:bg-teal-100/50 dark:hover:bg-teal-900/30 transition-all duration-200 text-foreground"
                    >
                      <div className="flex items-center gap-3">
                        <ChevronRight className={`h-4 w-4 text-teal-600 dark:text-teal-400 transition-transform duration-200 ${expandedSections.checkins ? 'rotate-90' : ''}`} />
                        <CalendarCheck className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                        <span className="text-sm font-semibold text-teal-700 dark:text-teal-300">Check-ins</span>
                      </div>
                      <span className="text-sm font-bold text-teal-700 dark:text-teal-300 bg-teal-200/50 dark:bg-teal-800/30 px-2 py-1 rounded-full">{upcomingCheckins.length}</span>
                    </button>
                    {expandedSections.checkins && (
                      <div className="border-t border-teal-200/50 dark:border-teal-800/30 p-4 space-y-2 max-h-40 overflow-y-auto overflow-x-hidden animate-in slide-in-from-top-2 duration-200">
                        {upcomingCheckins.slice(0, 10).map(property => (
                          <div
                            key={property.id}
                            className="flex justify-between items-center text-sm hover:bg-teal-100/50 dark:hover:bg-teal-900/30 p-3 rounded-lg cursor-pointer min-w-0 transition-all duration-200 group"
                            onClick={() => handlePropertyClick(property.id)}
                          >
                            <span className="font-medium truncate flex-1 min-w-0 group-hover:text-teal-600 dark:group-hover:text-teal-400">{property.name}</span>
                            <span className="text-teal-600 dark:text-teal-400 text-sm font-medium ml-2 flex-shrink-0">
                              {property.daysUntil === 0 ? 'Today' :
                               property.daysUntil === 1 ? 'Tomorrow' :
                               `${property.daysUntil}d`}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Empty State */}
                {criticalTasks.length === 0 && lowStockItems.length === 0 && newDamages.length === 0 && pendingOrders.length === 0 && upcomingCheckins.length === 0 && (
                  <div className="text-center py-8">
                    <Activity className="w-12 h-12 mx-auto mb-3 opacity-40" />
                    <p className="text-sm text-muted-foreground font-medium">All caught up!</p>
                    <p className="text-xs text-muted-foreground/70 mt-1">No immediate actions required</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// --- Compact Item Components (Potentially move to separate files later) ---

// Compact Task Item (Added Severity)
const CompactTaskItem = ({ title, subtitle, status, dueDate, onClick, severity }: {
  title: string;
  subtitle: string;
  status: string;
  dueDate: string;
  onClick: () => void;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}) => {
  const statusStyles = useMemo(() => {
    switch (status) {
      case 'new': return { icon: Circle, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'assigned': return { icon: Circle, color: 'text-purple-500 dark:text-purple-400', bg: 'bg-purple-100 dark:bg-purple-950/50' };
      case 'in_progress': return { icon: Circle, color: 'text-yellow-500 dark:text-yellow-400', bg: 'bg-yellow-100 dark:bg-yellow-950/50' };
      case 'completed': return { icon: Circle, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      default: return { icon: Circle, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  const severityStyles = useMemo(() => {
    switch (severity) {
      case 'critical': return "border-l-4 border-red-500";
      case 'high': return "border-l-4 border-amber-500";
      default: return "border-l-4 border-transparent";
    }
  }, [severity]);

  return (
    <div
      className={cn("flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors", severityStyles)}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-2 w-2", statusStyles.color)} fill={statusStyles.color} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{title}</p>
        <p className="text-xs text-muted-foreground truncate">{subtitle}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground whitespace-nowrap">{dueDate}</p>
        <p className={cn("text-xs capitalize font-medium", statusStyles.color)}>{status.replace('_', ' ')}</p>
      </div>
    </div>
  );
};

// Compact Inventory Item
const CompactInventoryItem = ({ name, quantity, minQuantity, onClick }: {
  name: string;
  quantity: number | null | undefined;
  minQuantity: number | null | undefined;
  onClick: () => void;
}) => {
  const isLow = typeof quantity === 'number' && typeof minQuantity === 'number' && quantity <= minQuantity;
  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        isLow && "border-l-4 border-purple-500"
      )}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", isLow ? "bg-purple-100 dark:bg-purple-950/50" : "bg-gray-100 dark:bg-gray-800")}>
        <Package className={cn("h-3 w-3", isLow ? "text-purple-600 dark:text-purple-400" : "text-gray-500 dark:text-gray-400")} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{name}</p>
      </div>
      <div className="ml-2 text-right">
        <p className={cn("text-xs font-semibold", isLow ? "text-purple-600 dark:text-purple-400" : "text-gray-700 dark:text-gray-300")}>
          {quantity ?? 'N/A'}
        </p>
        <p className="text-xs text-muted-foreground">Min: {minQuantity ?? 'N/A'}</p>
      </div>
    </div>
  );
};

// Compact Damage Item
const CompactDamageItem = ({ title, propertyName, reportedAt, status, onClick }: {
  title: string;
  propertyName: string;
  reportedAt: string;
  status: 'new' | 'pending' | 'completed';
  onClick: () => void;
}) => {
  const statusStyles = useMemo(() => {
    switch (status) {
      case 'new': return { icon: AlertCircle, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'pending': return { icon: AlertCircle, color: 'text-amber-500 dark:text-amber-400', bg: 'bg-amber-100 dark:bg-amber-950/50' };
      case 'completed': return { icon: AlertCircle, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      default: return { icon: AlertCircle, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        status === 'new' && "border-l-4 border-blue-500" // Highlight new damages
      )}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-3 w-3", statusStyles.color)} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{title}</p>
        <p className="text-xs text-muted-foreground truncate">{propertyName}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground whitespace-nowrap">{reportedAt}</p>
         <p className={cn("text-xs capitalize font-medium", statusStyles.color)}>{status}</p>
      </div>
    </div>
  );
};

// Compact Order Item
const CompactOrderItem = ({ orderNumber, supplier, status, itemCount, onClick }: {
  orderNumber: string;
  supplier: string; // Changed from 'supplier' to represent property_name now
  status: string;
  itemCount: number;
  onClick: () => void;
}) => {
   const statusStyles = useMemo(() => {
    switch (status) {
      case 'pending': return { icon: ShoppingCart, color: 'text-green-500 dark:text-green-400', bg: 'bg-green-100 dark:bg-green-950/50' };
      case 'ordered': return { icon: ShoppingCart, color: 'text-blue-500 dark:text-blue-400', bg: 'bg-blue-100 dark:bg-blue-950/50' };
      case 'received': return { icon: ShoppingCart, color: 'text-purple-500 dark:text-purple-400', bg: 'bg-purple-100 dark:bg-purple-950/50' };
      default: return { icon: ShoppingCart, color: 'text-gray-500 dark:text-gray-400', bg: 'bg-gray-100 dark:bg-gray-800' };
    }
  }, [status]);

  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
         status === 'pending' && "border-l-4 border-green-500" // Highlight pending orders
        )}
      onClick={onClick}
    >
       <span className={cn("mr-2 p-0.5 rounded-full", statusStyles.bg)}>
        <statusStyles.icon className={cn("h-3 w-3", statusStyles.color)} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{orderNumber}</p>
        {/* Display property name as subtitle */}
        <p className="text-xs text-muted-foreground truncate">{supplier}</p>
      </div>
      <div className="ml-2 text-right">
        <p className="text-xs text-muted-foreground">{itemCount} item(s)</p>
         <p className={cn("text-xs capitalize font-medium", statusStyles.color)}>{status}</p>
      </div>
    </div>
  );
};

// Compact Check-in Item
const CompactCheckinItem = ({ propertyName, daysUntil, onClick }: {
  propertyName: string;
  daysUntil: number;
  onClick: () => void;
}) => {
  const urgencyColor = useMemo(() => {
    if (daysUntil === 0) return "text-blue-600"; // Today
    if (daysUntil <= 3) return "text-amber-600"; // Soon
    return "text-green-600"; // Further out
  }, [daysUntil]);

  const urgencyText = useMemo(() => {
    if (daysUntil === 0) return "Today";
    if (daysUntil === 1) return "Tomorrow";
    return `In ${daysUntil} days`;
  }, [daysUntil]);

  return (
    <div
      className={cn(
        "flex items-center p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors",
        daysUntil <= 1 && "border-l-4 border-blue-500" // Highlight today/tomorrow
      )}
      onClick={onClick}
    >
      <span className={cn("mr-2 p-0.5 rounded-full", daysUntil <= 1 ? "bg-blue-100 dark:bg-blue-950/50" : "bg-teal-100 dark:bg-teal-950/50")}>
        <CalendarCheck className={cn("h-3 w-3", daysUntil <= 1 ? "text-blue-600 dark:text-blue-400" : "text-teal-600 dark:text-teal-400")} />
      </span>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium truncate">{propertyName}</p>
      </div>
      <div className="ml-2 text-right">
        <p className={cn("text-xs font-semibold whitespace-nowrap", urgencyColor)}>
          {urgencyText}
        </p>
      </div>
    </div>
  );
};


export default memo(DashboardView);
