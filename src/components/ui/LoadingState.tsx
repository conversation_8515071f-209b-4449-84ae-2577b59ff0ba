
import React from 'react';
import { Loader2 } from 'lucide-react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const loadingStateVariants = cva(
  "flex flex-col items-center justify-center p-8 text-center transition-all duration-300",
  {
    variants: {
      variant: {
        default: "",
        glass: 
          "glass-card rounded-xl backdrop-blur-xl shadow-lg",
        glass_minimal:
          "bg-white/10 dark:bg-black/10 backdrop-blur-sm rounded-lg border border-white/20 dark:border-white/10 shadow-md",
        floating:
          "glass-card rounded-2xl shadow-2xl bg-gradient-to-br from-white/25 to-white/10 dark:from-black/25 dark:to-black/10",
        minimal:
          "bg-white/5 dark:bg-black/5 backdrop-blur-sm rounded-md",
      },
      size: {
        default: "p-8",
        sm: "p-4",
        lg: "p-12",
        xl: "p-16",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const spinnerVariants = cva(
  "animate-spin mb-4 transition-all duration-300",
  {
    variants: {
      variant: {
        default: "text-primary",
        glass: "text-primary drop-shadow-lg",
        gradient: "text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary/70",
        glow: "text-primary animate-pulse-glow",
      },
      size: {
        default: "h-8 w-8",
        sm: "h-6 w-6",
        lg: "h-12 w-12",
        xl: "h-16 w-16",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface LoadingStateProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingStateVariants> {
  message?: string;
  spinnerVariant?: VariantProps<typeof spinnerVariants>['variant'];
  spinnerSize?: VariantProps<typeof spinnerVariants>['size'];
  showSpinner?: boolean;
}

const LoadingState: React.FC<LoadingStateProps> = ({ 
  message = 'Loading...',
  variant,
  size,
  spinnerVariant = 'default',
  spinnerSize = 'default',
  showSpinner = true,
  className,
  ...props
}) => {
  return (
    <div 
      className={cn(loadingStateVariants({ variant, size }), className)}
      {...props}
    >
      {showSpinner && (
        <Loader2 
          className={cn(spinnerVariants({ variant: spinnerVariant, size: spinnerSize }))} 
        />
      )}
      <p className="text-sm text-muted-foreground/80 text-body">{message}</p>
    </div>
  );
};

export default LoadingState;
export { loadingStateVariants, spinnerVariants };
