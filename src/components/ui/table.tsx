import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const tableVariants = cva(
  "w-full caption-bottom text-sm",
  {
    variants: {
      variant: {
        default: "",
        glass:
          "bg-white/5 dark:bg-black/5 backdrop-blur-sm border border-white/20 dark:border-white/10 rounded-lg shadow-lg overflow-hidden",
        glass_bordered:
          "bg-white/10 dark:bg-black/10 backdrop-blur-xl border border-white/30 dark:border-white/20 rounded-lg shadow-xl overflow-hidden",
        minimal:
          "bg-transparent",
        outline:
          "border border-white/30 dark:border-white/20 bg-transparent backdrop-blur-sm rounded-lg shadow-sm overflow-hidden",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface TableProps
  extends React.HTMLAttributes<HTMLTableElement>,
    VariantProps<typeof tableVariants> {}

const Table = React.forwardRef<HTMLTableElement, TableProps>(
  ({ className, variant, ...props }, ref) => (
    <div className="relative w-full overflow-auto">
      <table
        ref={ref}
        className={cn(tableVariants({ variant }), className)}
        {...props}
      />
    </div>
  )
)
Table.displayName = "Table"

const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead 
    ref={ref} 
    className={cn(
      "[&_tr]:border-b [&_tr]:border-white/20 dark:[&_tr]:border-white/10",
      "bg-gradient-to-r from-white/10 via-white/5 to-white/10 dark:from-black/10 dark:via-black/5 dark:to-black/10",
      "backdrop-blur-sm",
      className
    )} 
    {...props} 
  />
))
TableHeader.displayName = "TableHeader"

const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn(
      "[&_tr:last-child]:border-0",
      "bg-white/5 dark:bg-black/5 backdrop-blur-sm",
      className
    )}
    {...props}
  />
))
TableBody.displayName = "TableBody"

const TableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn(
      "border-t border-white/20 dark:border-white/10 font-medium [&>tr]:last:border-b-0",
      "bg-gradient-to-r from-white/10 via-white/5 to-white/10 dark:from-black/10 dark:via-black/5 dark:to-black/10",
      "backdrop-blur-sm",
      className
    )}
    {...props}
  />
))
TableFooter.displayName = "TableFooter"

const TableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b border-white/10 dark:border-white/5 transition-all duration-200",
      "hover:bg-white/10 dark:hover:bg-black/10 hover:backdrop-blur-sm",
      "data-[state=selected]:bg-white/15 dark:data-[state=selected]:bg-black/15",
      "data-[state=selected]:border-white/20 dark:data-[state=selected]:border-white/10",
      className
    )}
    {...props}
  />
))
TableRow.displayName = "TableRow"

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-12 px-4 text-left align-middle font-medium text-muted-foreground/80 [&:has([role=checkbox])]:pr-0",
      "bg-gradient-to-r from-white/5 to-transparent dark:from-black/5 dark:to-transparent",
      "font-semibold text-sm tracking-wide",
      className
    )}
    {...props}
  />
))
TableHead.displayName = "TableHead"

const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn(
      "p-4 align-middle [&:has([role=checkbox])]:pr-0",
      "text-body transition-all duration-200",
      className
    )}
    {...props}
  />
))
TableCell.displayName = "TableCell"

const TableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn(
      "mt-4 text-sm text-muted-foreground/80 text-caption",
      "bg-white/5 dark:bg-black/5 backdrop-blur-sm px-2 py-1 rounded-md",
      className
    )}
    {...props}
  />
))
TableCaption.displayName = "TableCaption"

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}
