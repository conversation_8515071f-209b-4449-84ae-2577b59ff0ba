import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const inputVariants = cva(
  "flex h-10 w-full px-3 py-2 text-base ring-offset-background transition-all duration-300 ease-in-out file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
  {
    variants: {
      variant: {
        default:
          "rounded-md border border-input bg-background hover:border-input/80 focus:border-primary/50",
        glass:
          "rounded-lg border border-white/30 dark:border-white/20 bg-white/10 dark:bg-black/10 backdrop-blur-xl shadow-lg hover:bg-white/15 dark:hover:bg-black/15 hover:border-white/40 dark:hover:border-white/30 focus:bg-white/20 dark:focus:bg-black/20 focus:border-white/50 dark:focus:border-white/40 focus:shadow-xl",
        glass_subtle:
          "rounded-lg border border-white/20 dark:border-white/10 bg-white/5 dark:bg-black/5 backdrop-blur-sm shadow-md hover:bg-white/10 dark:hover:bg-black/10 hover:border-white/30 dark:hover:border-white/20 focus:bg-white/15 dark:focus:bg-black/15 focus:border-white/40 dark:focus:border-white/30 focus:shadow-lg",
        outline:
          "rounded-lg border border-white/30 dark:border-white/20 bg-transparent backdrop-blur-sm hover:bg-white/5 dark:hover:bg-black/5 hover:border-white/40 dark:hover:border-white/30 focus:bg-white/10 dark:focus:bg-black/10 focus:border-primary/50 shadow-sm hover:shadow-md focus:shadow-lg",
        minimal:
          "rounded-md border-b-2 border-white/20 dark:border-white/10 bg-transparent backdrop-blur-sm px-2 py-1 hover:border-white/30 dark:hover:border-white/20 focus:border-primary/60 shadow-none",
      },
      size: {
        default: "h-10 px-3 py-2",
        sm: "h-9 px-2 py-1 text-sm",
        lg: "h-11 px-4 py-3 text-base",
        xl: "h-12 px-5 py-4 text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface InputProps
  extends React.ComponentProps<"input">,
    VariantProps<typeof inputVariants> {
  glowEffect?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, variant, size, glowEffect, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          inputVariants({ variant, size }),
          glowEffect && "focus:ring-4 focus:ring-primary/20",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input, inputVariants }
