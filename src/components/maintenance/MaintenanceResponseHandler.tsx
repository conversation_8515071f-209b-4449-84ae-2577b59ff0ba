import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';

interface TaskData {
  taskId: string;
  action: string;
  token: string;
  timestamp: string;
  providerEmail?: string;
}

const MaintenanceResponseHandler = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'auth_required'>('loading');
  const [message, setMessage] = useState('Processing your response...');
  const [details, setDetails] = useState<string | null>(null);
  const [providerEmail, setProviderEmail] = useState<string | null>(null);

  useEffect(() => {
    const processResponse = async () => {
      try {
        // Extract parameters from URL
        const taskId = searchParams.get('taskId');
        const action = searchParams.get('action');
        const token = searchParams.get('token');

        if (!taskId || !action || !token) {
          setStatus('error');
          setMessage('Missing required parameters. Please check the URL and try again.');
          setDetails(`Task ID: ${taskId || 'missing'}, Action: ${action || 'missing'}, Token: ${token ? 'provided' : 'missing'}`);
          return;
        }

        // Verify the token and get task details using supabase client
        try {
          console.log('Verifying maintenance token:', { taskId, action, token });
          let taskData;
          try {
            // Use URL parameters to avoid CORS issues
            const url = `${supabase.functions.url}/verify-maintenance-token?taskId=${encodeURIComponent(taskId)}&token=${encodeURIComponent(token)}`;
            console.log('Calling verify-maintenance-token with URL:', url);

            // Get the anon key from supabase client
            const anonKey = supabase.supabaseKey;

            const response = await fetch(url, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${anonKey}`
              }
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error('Error response from verify-maintenance-token:', { status: response.status, body: errorText });

              // Try to parse the error response
              try {
                const errorData = JSON.parse(errorText);

                // If the task is not found or has an invalid token, show a user-friendly message
                if (errorData.error === 'Not found' || errorData.error === 'Invalid token') {
                  setStatus('error');
                  setMessage('This maintenance task link is no longer valid.');
                  setDetails(errorData.message || 'The task may have been deleted or the link has expired.');
                  return;
                }
              } catch (parseError) {
                console.error('Error parsing error response:', parseError);
              }

              throw new Error(`HTTP error ${response.status}: ${errorText}`);
            }

            const data = await response.json();
            taskData = data;
            console.log('Token verification result:', { taskData });

            // Check if the response contains an error
            if (data.error) {
              console.error('Error from verify-maintenance-token function:', data.error);
              throw new Error(data.message || 'Error verifying token');
            }

            if (!taskData) {
              console.error('No task data returned from verify-maintenance-token function');
              throw new Error('No task data returned');
            }

            // Store the provider email from the task
            setProviderEmail(taskData.providerEmail);
          } catch (invokeError) {
            console.error('Error invoking verify-maintenance-token function:', invokeError);
            throw new Error('Failed to verify token: ' + (invokeError instanceof Error ? invokeError.message : String(invokeError)));
          }

          // If user is not authenticated and this is an accept or decline action
          console.log('Auth state:', authState);
          if (!authState.user && (action === 'accept' || action === 'decline' || action === 'reject')) {
            // Store task details in local storage
            const pendingTaskData: TaskData = {
              taskId,
              action,
              token,
              timestamp: new Date().toISOString(),
              providerEmail: taskData.providerEmail
            };
            localStorage.setItem('pendingMaintenanceTask', JSON.stringify(pendingTaskData));

            // Check if the current email matches a registered user
            const { data: userExists, error: userExistsError } = await supabase
              .from('service_providers')
              .select('id')
              .eq('email', taskData.providerEmail)
              .maybeSingle();

            console.log('User exists check:', { userExists, error: userExistsError });

            setStatus('auth_required');
            if (action === 'accept') {
              if (userExists) {
                setMessage('Please sign in to accept this maintenance task.');
                setDetails('An account already exists with this email address.');
              } else {
                setMessage('You need to create an account or sign in to accept this task.');
                setDetails('After creating your account, you\'ll be able to manage this and future maintenance tasks.');
              }
            } else { // decline action
              if (userExists) {
                setMessage('Please sign in to decline this maintenance task.');
                setDetails('An account already exists with this email address.');
              } else {
                setMessage('You need to create an account or sign in to decline this task.');
                setDetails('After creating your account, you can manage your maintenance tasks.');
              }
            }
            return;
          }

          // If authenticated, process the response using supabase client
          console.log('Processing maintenance response:', { taskId, action, token });

          // Use URL parameters to avoid CORS issues
          const responseUrl = `${supabase.functions.url}/maintenance-response?taskId=${encodeURIComponent(taskId)}&action=${encodeURIComponent(action)}&token=${encodeURIComponent(token)}`;
          console.log('Calling maintenance-response with URL:', responseUrl);

          // Get the anon key from supabase client
          const anonKey = supabase.supabaseKey;

          const responseResponse = await fetch(responseUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${anonKey}`
            }
          });

          if (!responseResponse.ok) {
            const errorText = await responseResponse.text();
            console.error('Error response from maintenance-response:', { status: responseResponse.status, body: errorText });

            // Parse the error response
            try {
              const errorData = JSON.parse(errorText);

              // Handle the case where the task has already been accepted/declined
              if (errorData.error === 'Invalid state' &&
                  (errorData.message.includes('already been accepted') ||
                   errorData.message.includes('already been declined') ||
                   errorData.message.includes('already been rejected'))) {

                console.log('Task has already been processed:', errorData.message);

                // Show a success message instead of an error
                setStatus('success');
                setMessage(errorData.message);
                return;
              }
            } catch (parseError) {
              console.error('Error parsing error response:', parseError);
            }

            throw new Error(`HTTP error ${responseResponse.status}: ${errorText}`);
          }

          const responseData = await responseResponse.json();

          if (responseData.error) {
            throw new Error(responseData.message || 'Error processing maintenance response');
          }

          console.log('Response processed successfully:', responseData);
          setStatus('success');
          setMessage(responseData?.message || `Task ${action === 'accept' ? 'accepted' : 'declined'} successfully.`);

          localStorage.removeItem('pendingMaintenanceTask');

          toast.success(responseData?.message || `Task ${action === 'accept' ? 'accepted' : 'declined'} successfully.`);

          setTimeout(() => {
            navigate('/maintenance', {
              state: {
                responseStatus: 'success',
                responseMessage: responseData?.message
              }
            });
          }, 2000);

        } catch (err: any) {
          console.error('Error verifying token:', err);
          setStatus('error');
          setMessage('Invalid or expired link. Please contact the property manager for a new link.');
          setDetails(err.message || 'Token verification failed');
          return;
        }
      } catch (err: any) {
        console.error('Error handling maintenance response:', err);
        setStatus('error');
        setMessage('Failed to process your response. Please try again or contact support.');
        setDetails(err.message || 'Unknown error');

        toast.error('Failed to process maintenance response');
      }
    };

    processResponse();
  }, [searchParams, navigate, authState.user]);

  const handleGoToMaintenance = () => {
    navigate('/maintenance');
  };

  const handleRegister = () => {
    // Store the current location so we can redirect back after registration
    sessionStorage.setItem('redirectAfterLogin', '/maintenance');

    // Use the proper navigation format for HashRouter
    navigate('/register', {
      state: {
        role: 'service_provider',
        email: providerEmail
      }
    });
  };

  const handleLogin = () => {
    // Store the current location so we can redirect back after login
    sessionStorage.setItem('redirectAfterLogin', '/maintenance');

    // Use the proper navigation format for HashRouter
    navigate('/login', {
      state: {
        email: providerEmail
      }
    });
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md p-6 space-y-6">
        <div className="flex flex-col items-center text-center space-y-4">
          {status === 'loading' && (
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />
          )}

          {status === 'success' && (
            <CheckCircle className="h-12 w-12 text-green-500" />
          )}

          {(status === 'error' || status === 'auth_required') && (
            <AlertCircle className="h-12 w-12 text-amber-500" />
          )}

          <h1 className="text-xl font-semibold">
            {status === 'loading' ? 'Processing' :
             status === 'success' ? 'Success' :
             status === 'auth_required' ? 'Account Required' : 'Error'}
          </h1>

          <p className="text-gray-600">{message}</p>

          {details && (status === 'error' || status === 'auth_required') && (
            <p className="text-xs text-gray-500 mt-2 max-w-full break-words">{details}</p>
          )}

          {status === 'auth_required' ? (
            <div className="flex flex-col gap-3 w-full max-w-xs">
              <Button
                onClick={handleRegister}
                className="w-full"
              >
                Create Service Provider Account
              </Button>
              <Button
                onClick={handleLogin}
                variant="outline"
                className="w-full"
              >
                Sign In
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                Creating an account allows you to manage maintenance tasks and receive updates.
              </p>
            </div>
          ) : (
            <Button
              onClick={handleGoToMaintenance}
              className="mt-4"
              variant={status === 'error' ? 'outline' : 'default'}
              disabled={status === 'loading'}
            >
              {status === 'loading' ? 'Please wait...' : 'Go to Maintenance Dashboard'}
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
};

export default MaintenanceResponseHandler;
