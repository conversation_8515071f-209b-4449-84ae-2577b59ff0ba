import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building, MapPin, Bed, Bath, Calendar, AlertTriangle, Wrench, Package, Wifi, WifiOff } from 'lucide-react';
import { checkImageExists } from '@/utils/propertyUtils';
import { PropertyStatistics } from '@/hooks/usePropertyStatistics';

export interface CollectionWithBudget {
  name: string;
  budget?: number;
}

export interface Property {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  imageUrl: string;
  bedrooms: number;
  bathrooms: number;
  budget: number;
  collections: CollectionWithBudget[];
  next_booking?: string;
  ical_url?: string;
  is_occupied?: boolean;
  current_checkout?: string;
  next_checkin_date?: string;
  next_checkin_formatted?: string;
  last_ical_sync?: string;
  timezone?: string;
  check_in_time?: string;
  check_out_time?: string;
}

interface PropertyCardProps {
  property: Property;
  onView: (property: Property) => void;
  onClick?: (property: Property) => void; // Optional click handler
  statistics?: PropertyStatistics | null; // Property statistics for indicators
}

const PropertyCard: React.FC<PropertyCardProps> = ({ property, onView, onClick, statistics }) => {
  const [imageExists, setImageExists] = useState<boolean | null>(null);

  // Debug logging for property data
  useEffect(() => {
    console.log(`[PropertyCard] Rendering property:`, {
      name: property.name,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      imageUrl: property.imageUrl
    });
  }, [property]);

  // Check if the image exists when the component mounts or the URL changes
  useEffect(() => {
    let isMounted = true;

    if (property.imageUrl) {
      // Set to null initially to show loading state
      if (isMounted) setImageExists(null);

      const checkImage = async () => {
        try {
          // Add a small delay to prevent too many simultaneous requests
          await new Promise(resolve => setTimeout(resolve, Math.random() * 500));

          const exists = await checkImageExists(property.imageUrl);

          // Only update state if component is still mounted
          if (isMounted) {
            setImageExists(exists);
            console.log(`[PropertyCard] Image exists for ${property.name}:`, exists);
          }
        } catch (error) {
          // Handle any unexpected errors
          console.error(`[PropertyCard] Error checking image for ${property.name}:`, error);
          if (isMounted) setImageExists(false);
        }
      };

      checkImage();
    } else {
      setImageExists(false);
    }

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [property.imageUrl, property.name]);

  // Use onClick if provided, otherwise fall back to onView
  const handleClick = () => {
    if (onClick) {
      onClick(property);
    } else {
      onView(property);
    }
  };

  // Ensure we have numeric values for bedrooms and bathrooms
  const bedrooms = typeof property.bedrooms === 'number' ? property.bedrooms :
                  parseInt(String(property.bedrooms || '0')) || 0;

  const bathrooms = typeof property.bathrooms === 'number' ? property.bathrooms :
                   parseInt(String(property.bathrooms || '0')) || 0;

  // Helper function to render status indicators
  const renderStatusIndicators = () => {
    if (!statistics) return null;

    const indicators = [];

    // Critical maintenance tasks
    if (statistics.maintenanceTasks.critical > 0) {
      indicators.push(
        <div key="critical" className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">
          <AlertTriangle className="h-3 w-3" />
          <span>{statistics.maintenanceTasks.critical} Critical</span>
        </div>
      );
    }

    // High priority maintenance tasks
    if (statistics.maintenanceTasks.high > 0) {
      indicators.push(
        <div key="high" className="flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium">
          <Wrench className="h-3 w-3 text-orange-700" />
          <span>{statistics.maintenanceTasks.high} High</span>
        </div>
      );
    }

    // Open damage reports
    if (statistics.damageReports.open > 0) {
      indicators.push(
        <div key="damages" className="flex items-center gap-1 px-2 py-1 bg-amber-100 text-amber-700 rounded-full text-xs font-medium">
          <AlertTriangle className="h-3 w-3 text-amber-700" />
          <span>{statistics.damageReports.open} Damage{statistics.damageReports.open > 1 ? 's' : ''}</span>
        </div>
      );
    }

    // Low stock items
    if (statistics.inventoryItems.outOfStock > 0) {
      indicators.push(
        <div key="outofstock" className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">
          <Package className="h-3 w-3 text-red-700" />
          <span>{statistics.inventoryItems.outOfStock} Out</span>
        </div>
      );
    } else if (statistics.inventoryItems.lowStock > 0) {
      indicators.push(
        <div key="lowstock" className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">
          <Package className="h-3 w-3 text-yellow-700" />
          <span>{statistics.inventoryItems.lowStock} Low</span>
        </div>
      );
    }

    return indicators.length > 0 ? (
      <div className="flex flex-wrap gap-1 mt-2">
        {indicators}
      </div>
    ) : null;
  };

  // Helper function to render sync status
  const renderSyncStatus = () => {
    if (!property.ical_url) {
      return (
        <div className="flex items-center gap-1 text-xs text-gray-400">
          <WifiOff className="h-3 w-3" />
          <span>No calendar</span>
        </div>
      );
    }

    const lastSync = property.last_ical_sync;
    const isRecentSync = lastSync && new Date(lastSync) > new Date(Date.now() - 24 * 60 * 60 * 1000); // Within 24 hours

    return (
      <div className={`flex items-center gap-1 text-xs ${isRecentSync ? 'text-green-600' : 'text-orange-600'}`}>
        {isRecentSync ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
        <span>{isRecentSync ? 'Synced' : 'Sync needed'}</span>
      </div>
    );
  };

  // Helper function to render property status badges
  const renderPropertyStatusBadges = () => {
    const badges = [];

    // Team-based badge
    if (property.team_name) {
      badges.push(
        <Badge key="team" variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:border-blue-800">
          {property.team_name}
        </Badge>
      );
    }

    // Property type badge (if available)
    if (property.property_type) {
      badges.push(
        <Badge key="type" variant="outline" className="text-xs bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700">
          {property.property_type}
        </Badge>
      );
    }

    return badges.length > 0 ? (
      <div className="flex flex-wrap gap-1 mt-2">
        {badges}
      </div>
    ) : null;
  };

  return (
    <div
      className="glass-card overflow-hidden group"
    >
      <div className="aspect-w-4 aspect-h-3 relative">
        {property.imageUrl && (imageExists === null || imageExists === true) ? (
          <>
            {/* Show loading indicator while checking image */}
            {imageExists === null && (
              <div className="absolute inset-0 flex items-center justify-center bg-muted/50 rounded-t-md z-10">
                <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            <img
              src={property.imageUrl}
              alt={property.name}
              className="w-full h-full object-cover rounded-t-md"
              loading="lazy" // Add lazy loading
              onError={(e) => {
                console.error("Property image failed to load:", property.imageUrl);
                setImageExists(false);

                // Try to load the image with a cache-busting parameter
                const cacheBustUrl = property.imageUrl + (property.imageUrl.includes('?') ? '&' : '?') + 'cb=' + new Date().getTime();
                console.log("Trying with cache-busting URL:", cacheBustUrl);

                // Set a fallback image if the retry fails
                e.currentTarget.onerror = () => {
                  console.error("Retry also failed, using placeholder");
                  e.currentTarget.src = "/placeholder.svg";
                  e.currentTarget.onerror = null; // Prevent infinite loop
                };

                // Try the cache-busting URL
                e.currentTarget.src = cacheBustUrl;
              }}
              onLoad={() => {
                // Confirm image exists when it loads successfully
                setImageExists(true);
              }}
            />
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-muted rounded-t-md">
            <Building className="h-12 w-12 text-muted-foreground/50" />
          </div>
        )}
      </div>
      <CardContent className="p-4 sm:p-5 md:p-6">
        <h3 className="text-lg font-semibold mb-2">{property.name}</h3>
        <div className="flex items-center text-muted-foreground mb-2">
          <MapPin className="h-4 w-4 mr-2" />
          <span>{property.city}, {property.state}</span>
        </div>
        <div className="grid grid-cols-2 gap-2 mb-3">
          <div className="flex items-center text-sm text-muted-foreground">
            <Bed className="h-4 w-4 mr-1" />
            <span>{bedrooms} Beds</span>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Bath className="h-4 w-4 mr-1" />
            <span>{bathrooms} Baths</span>
          </div>
        </div>
        {property.next_booking && (
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="h-4 w-4 mr-1" />
            <span>Next Booking: {property.next_booking}</span>
          </div>
        )}
        <div className="mt-2 space-y-2">
          {property.is_occupied && (
            <div className="flex items-center gap-2">
              <Badge variant="destructive">Occupied</Badge>
              {property.current_checkout && (
                <span className="text-xs text-muted-foreground">
                  Until {property.current_checkout}
                </span>
              )}
            </div>
          )}

          {/* Property status badges */}
          {renderPropertyStatusBadges()}

          {/* Status indicators */}
          {renderStatusIndicators()}

          {/* Sync status */}
          {renderSyncStatus()}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between items-center p-4 sm:p-5 md:p-6 pt-0">
        {property.collections && Array.isArray(property.collections) && property.collections.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {property.collections.map((collection, index) => (
              <Badge key={index} variant="secondary">
                {typeof collection === 'string' ? collection : collection.name}
              </Badge>
            ))}
          </div>
        )}
        <button
          onClick={handleClick}
          className="text-sm text-primary hover:underline"
        >
          View Details
        </button>
      </CardFooter>
    </div>
  );
};

export default PropertyCard;
