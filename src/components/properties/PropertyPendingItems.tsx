
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Wrench,
  AlertTriangle,
  ShoppingCart,
  ListTodo,
  Plus,
  Loader2
} from 'lucide-react';
import { PendingItem } from '@/hooks/usePendingItems';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MaintenanceTask, Provider } from '@/components/maintenance/types';
import MaintenanceDetailsDialog from '@/components/maintenance/MaintenanceDetailsDialog';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface PropertyPendingItemsProps {
  pendingItems: PendingItem[];
  propertyId: string;
  loading: boolean;
  onRefresh?: () => void;
}

const PropertyPendingItems: React.FC<PropertyPendingItemsProps> = ({
  pendingItems,
  propertyId,
  loading,
  onRefresh
}) => {
  const navigate = useNavigate();
  const { authState } = useAuth();
  const [selectedTask, setSelectedTask] = useState<MaintenanceTask | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch providers when component mounts
  useEffect(() => {
    const fetchProviders = async () => {
      if (!authState.user?.id) return;

      try {
        console.log('[PropertyPendingItems] Fetching providers for user:', authState.user.id);

        // Check if user is admin or super admin
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('is_super_admin, role')
          .eq('id', authState.user.id)
          .single();

        if (profileError) throw profileError;

        const isAdmin = profileData?.is_super_admin || profileData?.role === 'admin';
        console.log('[PropertyPendingItems] User is admin:', isAdmin);

        // Try our RPC function first
        try {
          console.log('[PropertyPendingItems] Using get_providers RPC function');
          const { data: rpcProviders, error: rpcError } = await supabase.rpc('get_providers');

          if (!rpcError && rpcProviders && rpcProviders.length > 0) {
            console.log(`[PropertyPendingItems] Found ${rpcProviders.length} providers with get_providers`);
            const formattedProviders: Provider[] = rpcProviders.map((provider: any) => ({
              id: provider.id,
              name: provider.name || '',
              email: provider.email || '',
              phone: provider.phone || '',
              specialty: provider.specialty || '',
              address: provider.notes || '',
              user_id: provider.user_id
            }));
            setProviders(formattedProviders);
            return;
          }
        } catch (rpcError) {
          console.error('[PropertyPendingItems] Exception with get_providers:', rpcError);
        }

        // Fallback to direct query
        const { data: ownProviders, error: ownError } = await supabase
          .from('maintenance_providers')
          .select('*')
          .eq('user_id', authState.user.id);

        if (ownError) throw ownError;

        if (ownProviders && ownProviders.length > 0) {
          const formattedProviders: Provider[] = ownProviders.map((provider: any) => ({
            id: provider.id,
            name: provider.name || '',
            email: provider.email || '',
            phone: provider.phone || '',
            specialty: provider.specialty || '',
            address: provider.notes || '',
            user_id: provider.user_id
          }));
          setProviders(formattedProviders);
        } else {
          console.log('[PropertyPendingItems] No providers available');
          setProviders([]);
        }
      } catch (error) {
        console.error('[PropertyPendingItems] Error fetching providers:', error);
      }
    };

    fetchProviders();
  }, [authState.user?.id]);

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-blue-500" />;
      case 'damage':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'inventory':
        return <ShoppingCart className="h-4 w-4 text-purple-500" />;
      default:
        return <ListTodo className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string, type: string) => {
    if (type === 'inventory') return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200';

    switch (status) {
      case 'new':
      case 'open':
        return 'bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200';
      case 'assigned':
      case 'in_progress':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'completed':
      case 'closed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      default:
        return 'bg-gray-100 dark:bg-gray-800/50 text-gray-800 dark:text-gray-200';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border-red-200 dark:border-red-800/50';
      case 'high':
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 border-orange-200 dark:border-orange-800/50';
      case 'medium':
        return 'bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 border-amber-200 dark:border-amber-800/50';
      case 'low':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border-green-200 dark:border-green-800/50';
      default:
        return 'bg-gray-100 dark:bg-gray-800/50 text-gray-800 dark:text-gray-200 border-gray-200 dark:border-gray-700/50';
    }
  };

  const fetchMaintenanceTask = async (taskId: string): Promise<MaintenanceTask | null> => {
    try {
      const { data, error } = await supabase
        .from('maintenance_tasks')
        .select(`
          *,
          properties(name)
        `)
        .eq('id', taskId)
        .single();

      if (error) {
        console.error('Error fetching maintenance task:', error);
        toast.error('Failed to load maintenance task details');
        return null;
      }

      if (!data) return null;

      // Transform the data to match MaintenanceTask interface
      return {
        id: data.id,
        title: data.title,
        description: data.description || '',
        propertyId: data.property_id,
        propertyName: data.properties?.name || 'Unknown Property',
        status: data.status,
        severity: data.severity,
        dueDate: data.due_date || '',
        assignedTo: data.assigned_to,
        createdAt: data.created_at,
        providerId: data.provider_id,
        providerEmail: data.provider_email,
        userId: data.user_id
      };
    } catch (error) {
      console.error('Error fetching maintenance task:', error);
      toast.error('Failed to load maintenance task details');
      return null;
    }
  };

  const handleViewItem = async (item: PendingItem) => {
    switch (item.type) {
      case 'maintenance':
        // Fetch the full maintenance task data and open the modal
        const task = await fetchMaintenanceTask(item.id);
        if (task) {
          setSelectedTask(task);
          setIsDetailsDialogOpen(true);
        }
        break;
      case 'damage':
        navigate(`/damages/${item.id}`);
        break;
      case 'inventory':
        navigate(`/inventory?focus=${item.id}`);
        break;
    }
  };

  const handleStatusChange = async (taskId: string, newStatus: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('maintenance_tasks')
        .update({ status: newStatus })
        .eq('id', taskId);

      if (error) {
        console.error('Error updating task status:', error);
        toast.error('Failed to update task status');
        return false;
      }

      toast.success(`Task status updated to ${newStatus}`);

      // Refresh the selected task data
      if (selectedTask && selectedTask.id === taskId) {
        const updatedTask = await fetchMaintenanceTask(taskId);
        if (updatedTask) {
          setSelectedTask(updatedTask);
        }
      }

      // Trigger a refresh of pending items
      setRefreshTrigger(prev => prev + 1);
      if (onRefresh) onRefresh();

      return true;
    } catch (error) {
      console.error('Error updating task status:', error);
      toast.error('Failed to update task status');
      return false;
    }
  };

  const handleDeleteTask = async (taskId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('maintenance_tasks')
        .delete()
        .eq('id', taskId);

      if (error) {
        console.error('Error deleting task:', error);
        toast.error('Failed to delete task');
        return false;
      }

      toast.success('Task deleted successfully');

      // Close the dialog
      setIsDetailsDialogOpen(false);
      setSelectedTask(null);

      // Trigger a refresh of pending items
      setRefreshTrigger(prev => prev + 1);
      if (onRefresh) onRefresh();

      return true;
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error('Failed to delete task');
      return false;
    }
  };

  const handleTaskUpdated = () => {
    // Refresh the pending items when a task is updated
    setRefreshTrigger(prev => prev + 1);
    if (onRefresh) onRefresh();

    // Refresh the selected task data if it's still open
    if (selectedTask) {
      fetchMaintenanceTask(selectedTask.id).then(updatedTask => {
        if (updatedTask) {
          setSelectedTask(updatedTask);
        }
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pending Items</CardTitle>
        <CardDescription>
          View all maintenance tasks, damage reports, and inventory needs for this property
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : pendingItems.length > 0 ? (
          <div className="space-y-2">
            {pendingItems.map((item) => (
              <div 
                key={`${item.type}-${item.id}`} 
                className="flex justify-between items-center p-3 border rounded-lg cursor-pointer hover:bg-muted/50"
                onClick={() => handleViewItem(item)}
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-muted">
                    {getItemIcon(item.type)}
                  </div>
                  <div>
                    <p className="font-medium">{item.title}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getStatusColor(item.status, item.type)}>
                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                      </Badge>
                      {item.priority && item.type === 'maintenance' && (
                        <Badge className={getSeverityColor(item.priority)}>
                          {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)} Priority
                        </Badge>
                      )}
                      {item.priority && item.type !== 'maintenance' && (
                        <Badge variant="outline">
                          {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)} Priority
                        </Badge>
                      )}
                      <span className="text-xs text-muted-foreground">
                        {new Date(item.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="sm">View</Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-muted/30 rounded-lg">
            <p className="text-muted-foreground">No pending items for this property.</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => navigate(`/maintenance?property=${propertyId}`)}>
          <Plus className="mr-2 h-4 w-4" />
          New Maintenance
        </Button>
        <Button variant="outline" onClick={() => navigate(`/damages/add?property=${propertyId}`)}>
          <Plus className="mr-2 h-4 w-4" />
          Report Damage
        </Button>
      </CardFooter>

      {/* Maintenance Details Dialog */}
      {selectedTask && (
        <MaintenanceDetailsDialog
          open={isDetailsDialogOpen}
          onOpenChange={setIsDetailsDialogOpen}
          task={selectedTask}
          providers={providers}
          onStatusChange={handleStatusChange}
          onDeleteTask={handleDeleteTask}
          onTaskUpdated={handleTaskUpdated}
        />
      )}
    </Card>
  );
};

export default PropertyPendingItems;
