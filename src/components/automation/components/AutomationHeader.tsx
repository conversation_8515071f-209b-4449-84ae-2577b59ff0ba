import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Plus, RefreshCw } from 'lucide-react';

interface AutomationHeaderProps {
  onAddRule: () => void;
  onProcessAll: () => void;
  isProcessing: boolean;
  canProcess: boolean;
}

export const AutomationHeader: React.FC<AutomationHeaderProps> = ({
  onAddRule,
  onProcessAll,
  isProcessing,
  canProcess
}) => {
  return (
    <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
      <div>
        <h2 className="text-2xl font-bold">Task Automation Rules</h2>
        <p className="text-muted-foreground">
          Create rules to automatically generate tasks based on bookings
        </p>
        <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800/50 rounded-md text-sm">
          <p className="text-blue-800 dark:text-blue-200 font-medium">Important:</p>
          <p className="text-blue-700 dark:text-blue-300">
            Make sure to set the correct timezone and check-in/check-out times for each property in the property settings.
            These settings are used to calculate the exact time when tasks should be created.
          </p>
        </div>
      </div>
      <div className="flex flex-wrap gap-2">
        <Button
          variant="outline"
          onClick={onProcessAll}
          disabled={isProcessing || !canProcess}
          className="flex-1 md:flex-none"
        >
          {isProcessing ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Run Automation
        </Button>
        <Button
          onClick={onAddRule}
          className="flex-1 md:flex-none"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Rule
        </Button>
      </div>
    </div>
  );
};