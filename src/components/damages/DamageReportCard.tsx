
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import { Wrench, Building, ArrowRight, Trash2, Loader2 } from 'lucide-react'; // Import Loader2
import { DamageReport } from '@/types/damages';
import { toast } from 'sonner'; // Import toast
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface DamageReportCardProps {
  report: DamageReport;
  onViewDetails?: (reportId: string) => void;
  onView?: (reportId: string) => void; // Added for compatibility
  onDelete?: (reportId: string) => Promise<void> | void;
}

const statusColors: Record<string, string> = {
  open: 'bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-200 dark:bg-amber-950/50 dark:text-amber-300 dark:border-amber-900 dark:hover:bg-amber-900/50',
  pending: 'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200 dark:bg-purple-950/50 dark:text-purple-300 dark:border-purple-900 dark:hover:bg-purple-900/50',
  in_progress: 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-900 dark:hover:bg-blue-900/50',
  completed: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200 dark:bg-green-950/50 dark:text-green-300 dark:border-green-900 dark:hover:bg-green-900/50',
  rejected: 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200 dark:bg-red-950/50 dark:text-red-300 dark:border-red-900 dark:hover:bg-red-900/50'
};

const DamageReportCard = ({ report, onViewDetails, onView, onDelete }: DamageReportCardProps) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false); // Add deleting state

  const getStatusColor = (status: string) => {
    return statusColors[status] || 'bg-gray-100 text-gray-800';
  };

  const timeAgo = report.created_at
    ? formatDistanceToNow(new Date(report.created_at), { addSuffix: true })
    : 'recently';

  const formatStatus = (status: string) => {
    switch(status) {
      case 'open': return 'Open';
      case 'pending': return 'Pending';
      case 'in_progress': return 'In Progress';
      case 'completed': return 'Completed';
      case 'rejected': return 'Rejected';
      default: return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Use either onView or onViewDetails callback, preferring onViewDetails if both exist
  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(report.id);
    } else if (onView) {
      onView(report.id);
    }
  };

  const handleDeleteRequest = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!onDelete) return;

    setIsDeleting(true);
    try {
      await onDelete(report.id);
      // No need to close dialog here, onOpenChange handles it if successful
      // Optionally add a success toast here if onDelete doesn't handle it
    } catch (error) {
      console.error("Failed to delete damage report:", error);
      toast.error("Failed to delete report. Please try again.");
      setIsDeleteDialogOpen(false); // Keep dialog open on error maybe? Or close it. Closing for now.
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <div
        className="cursor-pointer hover:shadow-md transition-all duration-200"
        onClick={handleViewDetails}
      >
        <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <CardTitle className="text-lg font-semibold">{report.title}</CardTitle>
            <Badge className={getStatusColor(report.status)}>
              {formatStatus(report.status)}
            </Badge>
          </div>
          <CardDescription>
            {report.property_name && (
              <span className="flex items-center text-xs mt-1">
                <Building className="h-3 w-3 mr-1" />
                <span>{report.property_name}</span>
              </span>
            )}
            {report.provider_name && (
              <span className="flex items-center text-xs mt-1">
                <Wrench className="h-3 w-3 mr-1" />
                <span>{report.provider_name}</span>
              </span>
            )}
            <span className="block text-xs mt-1">Reported {timeAgo}</span>
            {report.platform && <span className="block text-xs mt-1">Platform: {report.platform}</span>}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm line-clamp-3">{report.description}</div>
        </CardContent>
        <CardFooter className="pt-2 flex flex-col sm:flex-row justify-end gap-2">
          {(onViewDetails || onView) && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              className="w-full sm:w-auto"
            >
              View Details <ArrowRight className="ml-1 h-3 w-3" />
            </Button>
          )}
          {onDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeleteRequest}
              className="w-full sm:w-auto text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-3 w-3 mr-1" /> Delete
            </Button>
          )}
        </CardFooter>
      </Card>
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the damage report "{report.title}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting} // Disable while deleting
              className="bg-red-600 text-white hover:bg-red-700 flex items-center gap-2"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DamageReportCard;
