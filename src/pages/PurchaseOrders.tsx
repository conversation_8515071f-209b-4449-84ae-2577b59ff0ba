import React, { useState, useEffect, useMemo } from 'react';

import { Package, Search, Plus, ShoppingCart, Filter, CheckCircle, Archive, RefreshCw } from 'lucide-react';
import { StandardPageHeader, SearchBar, FilterBar, FilterDropdown, FilterOption } from '@/components/ui/StandardizedUI';
import CreatePurchaseOrderDialog from '@/components/inventory/CreatePurchaseOrderDialog';
import PurchaseOrderDetailsDialog from '@/components/inventory/PurchaseOrderDetailsDialog';
import { useDisclosure } from '@/hooks/useDisclosure';
import { useNavigate } from 'react-router-dom';
import { useInventoryQueryV2 } from '@/hooks/useInventoryQueryV2';
import { usePurchaseOrders } from '@/hooks/usePurchaseOrders';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { POStatus } from '@/types/inventory';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/contexts/AuthContext';
import PermissionGuard from '@/components/common/PermissionGuard';
import { PermissionType } from '@/types/auth';

const PurchaseOrders = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { authState } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<POStatus | null>(null);
  const [selectedOrder, setSelectedOrder] = useState(null);

  const createOrderDialog = useDisclosure();
  const orderDetailsDialog = useDisclosure();

  const [debugInfo, setDebugInfo] = useState({
    lastOrderAttempt: null as any,
    createOrderCalled: false
  });

  const {
    purchaseOrders,
    isLoading,
    isError,
    error,
    createPurchaseOrder,
    updateOrderStatus,
    updateOrderItemQuantity,
    deletePurchaseOrder,
    retryFetch
  } = usePurchaseOrders();

  // We don't need this effect anymore as the retry logic is now in the usePurchaseOrders hook

  const { inventoryItems, loading: loadingInventory } = useInventoryQueryV2();

  const { data: properties = [] } = useQuery({
    queryKey: ['unique_properties'],
    queryFn: async () => {
      console.log('[PurchaseOrders] Fetching unique properties');

      if (!authState.user?.id) {
        console.log('[PurchaseOrders] No authenticated user, skipping properties fetch');
        return [];
      }

      // Use our new RPC function to get unique properties
      const { data, error } = await supabase.rpc(
        'get_unique_user_properties',
        { p_user_id: authState.user.id }
      );

      if (error) {
        console.error('[PurchaseOrders] Error fetching unique properties with RPC:', error);

        // Fall back to direct query with deduplication
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('properties')
          .select('id, name')
          .order('name');

        if (fallbackError) {
          console.error('[PurchaseOrders] Error fetching properties fallback:', fallbackError);
          throw fallbackError;
        }

        // Deduplicate properties
        // Create a map to store unique properties by name
        const uniquePropertiesByName = new Map<string, { id: string; name: string }>();

        // Process properties to keep only one entry per property name
        for (const property of fallbackData || []) {
          if (!uniquePropertiesByName.has(property.name.toLowerCase())) {
            uniquePropertiesByName.set(property.name.toLowerCase(), property);
          }
        }

        // Convert the map values to an array
        const uniqueProperties = Array.from(uniquePropertiesByName.values());

        console.log(`[PurchaseOrders] Properties loaded (fallback): ${uniqueProperties.length}`);
        return uniqueProperties;
      }

      // Map the result to just id and name
      const formattedProperties = data.map((property: any) => ({
        id: property.id,
        name: property.name
      }));

      console.log('[PurchaseOrders] Unique properties loaded:', formattedProperties.length);
      return formattedProperties;
    },
    enabled: !!authState.user?.id
  });

  const formatInventoryItems = () => {
    if (!inventoryItems) return [];

    const formatted = inventoryItems.map(item => ({
      id: item.id,
      name: item.name,
      propertyId: item.property_id || item.propertyId,
      property_id: item.property_id || item.propertyId,
      propertyName: item.propertyName || item.property_name || 'Unknown Property',
      property_name: item.propertyName || item.property_name || 'Unknown Property',
      collection: item.collection || '',
      quantity: item.quantity || 0,
      minQuantity: item.min_quantity || item.minQuantity || 1,
      min_quantity: item.min_quantity || item.minQuantity || 1,
      price: item.price || 0,
      amazonUrl: item.amazon_url || item.amazonUrl || '',
      amazon_url: item.amazon_url || item.amazonUrl || '',
      walmartUrl: item.walmart_url || item.walmartUrl || '',
      walmart_url: item.walmart_url || item.walmartUrl || '',
      imageUrl: item.image_url || item.imageUrl || '',
      image_url: item.image_url || item.imageUrl || '',
      lastOrdered: item.last_ordered || item.lastOrdered || '',
      last_ordered: item.last_ordered || item.lastOrdered || ''
    }));

    console.log('[PurchaseOrders] Formatted inventory items:', formatted.length);
    return formatted;
  };

  const formattedInventory = formatInventoryItems();

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch {
      return dateString;
    }
  };

  const filteredOrders = purchaseOrders
    ? purchaseOrders.filter(order => {
        if (statusFilter === null && order.status === 'archived') {
          return false;
        }

        const matchesSearch =
          (order.property_name?.toLowerCase().includes(searchQuery.toLowerCase()) || '') ||
          order.items?.some(item => item.item_name.toLowerCase().includes(searchQuery.toLowerCase()));

        const matchesStatus = statusFilter === null || order.status === statusFilter;

        return matchesSearch && matchesStatus;
      })
    : [];

  const handleCreateOrder = () => {
    console.log('[PurchaseOrders] Opening create order dialog');
    console.log('[PurchaseOrders] Available properties:', properties);
    console.log('[PurchaseOrders] Available inventory items:', formattedInventory.length);
    createOrderDialog.onOpen();
  };

  const handleSavePurchaseOrder = (orderData: any) => {
    console.log('[PurchaseOrders] handleSavePurchaseOrder called with data:', orderData);
    setDebugInfo({
      lastOrderAttempt: orderData,
      createOrderCalled: true
    });

    if (!orderData.items || orderData.items.length === 0) {
      console.error('[PurchaseOrders] No items in order data');
      toast.error('No items selected. Please select at least one item.');
      return;
    }

    // Group items by property
    const itemsByProperty = orderData.items.reduce((acc: Record<string, any[]>, item: any) => {
      // Use the item's original property ID if available, otherwise use the order's property ID
      const propertyId = item.propertyId || orderData.propertyId;

      if (!acc[propertyId]) {
        acc[propertyId] = [];
      }

      acc[propertyId].push(item);
      return acc;
    }, {} as Record<string, any[]>);

    console.log('[PurchaseOrders] Items grouped by property:', itemsByProperty);

    // If we have items from multiple properties, create separate orders
    const propertyIds = Object.keys(itemsByProperty);

    if (propertyIds.length > 1) {
      console.log('[PurchaseOrders] Creating separate orders for multiple properties');

      // Create a separate order for each property
      let successCount = 0;
      let totalProperties = propertyIds.length;

      // Process each property's items
      propertyIds.forEach(propertyId => {
        const items = itemsByProperty[propertyId];
        const propertyName = items[0]?.propertyName || 'Unknown Property';

        console.log(`[PurchaseOrders] Processing items for property: ${propertyId} (${propertyName})`);

        // Format the items for this property
        const formattedItems = items.map(item => ({
          inventory_item_id: item.inventory_item_id || item.id,
          item_name: item.item_name || item.name,
          quantity: item.quantity || item.orderQuantity || 1,
          price: item.price || 0,
          amazon_url: item.amazon_url || item.amazonUrl || '',
          walmart_url: item.walmart_url || item.walmartUrl || ''
        }));

        // Calculate total price for this property's items
        const totalPrice = formattedItems.reduce((sum, item) =>
          sum + (item.price || 0) * item.quantity, 0);

        // Create order data for this property
        const propertyOrderData = {
          property_id: propertyId,
          status: 'pending' as POStatus,
          total_price: totalPrice,
          notes: orderData.notes || '',
          is_archived: false
        };

        console.log(`[PurchaseOrders] Creating order for property ${propertyId} with ${formattedItems.length} items`);

        // Create the purchase order for this property
        createPurchaseOrder.mutate({
          orderData: propertyOrderData,
          items: formattedItems
        }, {
          onSuccess: () => {
            console.log(`[PurchaseOrders] Successfully created order for property: ${propertyId}`);
            successCount++;

            // If this is the last property, close the dialog
            if (successCount === totalProperties) {
              createOrderDialog.onClose();
              toast.success(`Created ${totalProperties} purchase orders successfully`);
            }
          },
          onError: (error) => {
            console.error(`[PurchaseOrders] Error creating order for property ${propertyId}:`, error);
            toast.error(`Failed to create order for ${propertyName}: ${error.message}`);
          }
        });
      });

      // Return early since we're handling the orders separately
      return;
    }

    // If we only have one property, proceed with the original logic
    // Validate property ID
    if (!orderData.propertyId && propertyIds.length === 1) {
      // Use the property ID from the grouped items
      orderData.propertyId = propertyIds[0];
      console.log(`[PurchaseOrders] Using property ID from items: ${orderData.propertyId}`);
    } else if (!orderData.propertyId) {
      console.error('[PurchaseOrders] Property ID is missing in order data');

      // Try to find a property ID in the items
      if (orderData.items && orderData.items.length > 0) {
        // Look for a property ID in the properties array
        const firstItem = orderData.items[0];
        const itemPropertyId = firstItem.property_id || firstItem.propertyId;

        if (itemPropertyId) {
          console.log(`[PurchaseOrders] Found property ID in first item: ${itemPropertyId}`);
          orderData.propertyId = itemPropertyId;
        } else if (properties.length > 0) {
          // If we still don't have a property ID, use the first available property
          orderData.propertyId = properties[0].id;
          console.log(`[PurchaseOrders] Using first available property: ${orderData.propertyId}`);
          toast.info(`Using property: ${properties[0].name}`);
        } else {
          toast.error('No valid property found. Please select a property and try again.');
          return;
        }
      } else if (properties.length > 0) {
        // If we don't have items, use the first available property
        orderData.propertyId = properties[0].id;
        console.log(`[PurchaseOrders] Using first available property: ${orderData.propertyId}`);
        toast.info(`Using property: ${properties[0].name}`);
      } else {
        toast.error('No valid property found. Please select a property and try again.');
        return;
      }
    }

    // Validate property exists
    const propertyExists = properties.some(p => p.id === orderData.propertyId);
    if (!propertyExists) {
      console.warn(`[PurchaseOrders] Property ID ${orderData.propertyId} not found in properties array`);
      console.log('[PurchaseOrders] Available properties:', properties);

      // If the property doesn't exist, try to use the first available property
      if (properties.length > 0) {
        orderData.propertyId = properties[0].id;
        console.log(`[PurchaseOrders] Property not found, using first available property: ${orderData.propertyId}`);
        toast.info(`Using property: ${properties[0].name}`);
      }
    }

    const dbOrderData = {
      property_id: orderData.propertyId,
      status: 'pending' as POStatus,
      total_price: orderData.totalPrice,
      notes: orderData.notes || '',
      is_archived: false
    };

    const orderItems = orderData.items.map(item => ({
      inventory_item_id: item.inventory_item_id || item.id,
      item_name: item.item_name || item.name,
      quantity: item.quantity || item.orderQuantity || 1,
      price: item.price || 0,
      amazon_url: item.amazon_url || item.amazonUrl || '',
      walmart_url: item.walmart_url || item.walmartUrl || ''
    }));

    console.log('[PurchaseOrders] Creating purchase order with:', {
      orderData: dbOrderData,
      items: orderItems
    });

    createPurchaseOrder.mutate({
      orderData: dbOrderData,
      items: orderItems
    }, {
      onSuccess: () => {
        console.log('[PurchaseOrders] Purchase order created successfully');
        createOrderDialog.onClose();
        toast.success('Purchase order created successfully');
      },
      onError: (error) => {
        console.error('[PurchaseOrders] Error creating purchase order:', error);
        toast.error(`Failed to create purchase order: ${error.message}`);
      }
    });
  };

  const handleViewOrder = (order: any) => {
    console.log("Viewing order with items:", order.items);
    setSelectedOrder(order);
    orderDetailsDialog.onOpen();
  };

  const handleCheckout = (orderId: string) => {
    console.log("Checking out order:", orderId);

    updateOrderStatus.mutate({
      orderId,
      status: 'ordered'
    }, {
      onSuccess: () => {
        toast.success("Order status updated to 'ordered'");

        orderDetailsDialog.onOpen();
      },
      onError: (error) => {
        console.error("Error updating order status:", error);
        toast.error("Failed to update order status");
      }
    });
  };

  const handleDeleteOrder = (orderId: string) => {
    deletePurchaseOrder.mutate(orderId);
    orderDetailsDialog.onClose();
  };

  const handleUpdateOrderStatus = (orderId: string, status: POStatus) => {
    updateOrderStatus.mutate({ orderId, status });
  };

  const handleEditItemQuantity = (orderId: string, itemId: string, quantity: number) => {
    updateOrderItemQuantity.mutate({ itemId, quantity, orderId });
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'ordered':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'delivered':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'archived':
        return 'bg-gray-100 dark:bg-gray-800/50 text-gray-800 dark:text-gray-200';
      default:
        return 'bg-gray-100 dark:bg-gray-800/50 text-gray-800 dark:text-gray-200';
    }
  };

  useEffect(() => {
    console.log('[PurchaseOrders] Debug Info:', debugInfo);

    if (debugInfo.createOrderCalled) {
      console.log('[PurchaseOrders] Create order was called with:', debugInfo.lastOrderAttempt);
    }
  }, [debugInfo]);

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <StandardPageHeader
          title="Purchase Orders"
          description="Manage your property purchase orders"
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          searchPlaceholder="Search orders..."
          onRefresh={retryFetch}
          isLoading={isLoading}
          primaryActionLabel={isMobile ? "New" : "New Order"}
          onPrimaryAction={handleCreateOrder}
          primaryActionIcon={<Plus size={16} className="mr-1" />}
        />

        <FilterBar className="mb-3">
          <FilterDropdown
            label="Status"
            value={statusFilter === null ? 'all' : statusFilter}
            onChange={(value) => setStatusFilter(value === 'all' ? null : value as POStatus)}
          >
            <FilterOption value="all">All</FilterOption>
            <FilterOption value="pending">Pending</FilterOption>
            <FilterOption value="ordered">Ordered</FilterOption>
            <FilterOption value="delivered">Delivered</FilterOption>
            <FilterOption value="archived">Archived</FilterOption>
          </FilterDropdown>
        </FilterBar>

        {isLoading ? (
          <div className="flex items-center justify-center h-48">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredOrders.length > 0 ? (
              filteredOrders.map(order => (
                <div
                  key={order.id}
                  className="glass p-3 md:p-4 rounded-xl cursor-pointer hover:bg-primary/5 transition-colors"
                  onClick={() => handleViewOrder(order)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-medium text-base md:text-lg">{order.property_name || 'Unknown Property'}</h3>
                      <div className="flex items-center gap-2 mt-1 flex-wrap">
                        <span className="text-xs md:text-sm text-muted-foreground">{formatDate(order.created_at)}</span>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusBadgeClass(order.status)}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-base md:text-lg">${order.total_price?.toFixed(2) || '0.00'}</div>
                      <div className="text-xs md:text-sm text-muted-foreground">{order.items?.length || 0} items</div>
                    </div>
                  </div>

                  {order.items && order.items.length > 0 && (
                    <div className="border-t border-border pt-2 mt-2">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {order.items.slice(0, 2).map(item => (
                          <div key={item.id} className="flex justify-between items-center">
                            <div>
                              <div className="font-medium text-xs md:text-sm">{item.item_name}</div>
                              <div className="text-xs text-muted-foreground">Qty: {item.quantity}</div>
                            </div>
                            <div className="font-medium text-xs md:text-sm">${item.price?.toFixed(2) || '0.00'}</div>
                          </div>
                        ))}
                        {order.items.length > 2 && (
                          <div className="text-xs text-primary font-medium md:col-span-2 text-center mt-1">
                            + {order.items.length - 2} more items
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end gap-2 mt-2 pt-2 border-t border-border">
                    <button
                      className="text-xs md:text-sm text-primary font-medium hover:underline"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewOrder(order);
                      }}
                    >
                      View Details
                    </button>
                    {order.status === 'pending' && (
                      <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                        <button
                          id={`checkout-button-${order.id}`}
                          className="text-xs md:text-sm bg-primary text-white px-2 md:px-3 py-1 rounded-lg font-medium flex items-center gap-1.5"
                          onClick={(e) => {
                            e.stopPropagation();

                            setSelectedOrder(order);

                            handleCheckout(order.id);

                            orderDetailsDialog.onOpen();
                          }}
                        >
                          <ShoppingCart size={14} />
                          Checkout
                        </button>
                      </PermissionGuard>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-10 glass rounded-xl">
                <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                  <ShoppingCart size={24} className="text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-1">No purchase orders found</h3>
                <p className="text-muted-foreground mb-4 text-sm">
                  {searchQuery || statusFilter ?
                    "Try adjusting your filters" :
                    "Create your first purchase order to get started"}
                </p>

                {isError && (
                  <div className="flex flex-col items-center justify-center mt-2">
                    <button
                      onClick={retryFetch}
                      className="flex items-center gap-1.5 text-primary hover:underline"
                    >
                      <RefreshCw size={16} />
                      Retry loading
                    </button>
                  </div>
                )}

                {!isError && !searchQuery && !statusFilter && (
                  <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                    <button
                      onClick={handleCreateOrder}
                      className="mt-2 inline-flex items-center gap-1.5 px-4 py-2 bg-primary text-white rounded-lg"
                    >
                      <Plus size={16} />
                      Create Purchase Order
                    </button>
                  </PermissionGuard>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      <CreatePurchaseOrderDialog
        isOpen={createOrderDialog.isOpen}
        onClose={createOrderDialog.onClose}
        items={formattedInventory}
        properties={properties}
        onSave={handleSavePurchaseOrder}
      />

      {selectedOrder && (
        <PurchaseOrderDetailsDialog
          isOpen={orderDetailsDialog.isOpen}
          onClose={orderDetailsDialog.onClose}
          order={selectedOrder}
          onDelete={handleDeleteOrder}
          onCheckout={handleCheckout}
          onUpdateStatus={handleUpdateOrderStatus}
          onEditQuantity={handleEditItemQuantity}
        />
      )}
    </>
  );
};

export default PurchaseOrders;
