import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { usePropertiesRebuild } from '@/hooks/usePropertiesRebuild'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, RefreshCw, Home, MapPin, Bed, Bath } from 'lucide-react'

const PropertiesRebuild: React.FC = () => {
  const { authState, signOut } = useAuth()
  const { properties, loading, error, refetch } = usePropertiesRebuild()

  if (authState.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading...</span>
      </div>
    )
  }

  if (!authState.isAuthenticated) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Not Authenticated</h1>
        <p>Please log in to view your properties.</p>
      </div>
    )
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Properties (Rebuilt)</h1>
            <p className="text-gray-600">Clean, simple implementation</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={refetch} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={signOut} variant="outline" size="sm">
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      {/* Auth Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Authentication Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>User ID:</strong> {authState.user?.id}</p>
            <p><strong>Email:</strong> {authState.user?.email}</p>
            <p><strong>Authenticated:</strong> {authState.isAuthenticated ? '✅ Yes' : '❌ No'}</p>
          </div>
        </CardContent>
      </Card>

      {/* Properties */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Home className="h-5 w-5" />
            Your Properties
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading properties...
            </div>
          )}
          
          {error && (
            <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800/50 rounded p-4 mb-4">
              <p className="text-red-800 dark:text-red-200"><strong>Error:</strong> {error}</p>
              <Button onClick={refetch} variant="outline" size="sm" className="mt-2">
                Try Again
              </Button>
            </div>
          )}

          {!loading && !error && (
            <div>
              <p className="mb-4 text-sm text-gray-600">
                <strong>Found {properties.length} properties</strong>
              </p>
              
              {properties.length === 0 ? (
                <div className="text-center py-8">
                  <Home className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No properties found.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Add your first property to get started.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {properties.map((property) => (
                    <Card key={property.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <h3 className="font-semibold text-lg mb-2">{property.name}</h3>
                        
                        <div className="flex items-start gap-2 mb-3">
                          <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                          <p className="text-sm text-gray-600">
                            {property.address}<br />
                            {property.city}, {property.state} {property.zip}
                          </p>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Bed className="h-4 w-4" />
                            {property.bedrooms} bed
                          </div>
                          <div className="flex items-center gap-1">
                            <Bath className="h-4 w-4" />
                            {property.bathrooms} bath
                          </div>
                        </div>
                        
                        <div className="mt-3 pt-3 border-t text-xs text-gray-400">
                          Created: {new Date(property.created_at).toLocaleDateString()}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Debug Info */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
            <p><strong>Error:</strong> {error || 'None'}</p>
            <p><strong>Properties Count:</strong> {properties.length}</p>
            <p><strong>Auth Loading:</strong> {authState.isLoading ? 'Yes' : 'No'}</p>
            <p><strong>Has Session:</strong> {authState.session ? 'Yes' : 'No'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PropertiesRebuild
