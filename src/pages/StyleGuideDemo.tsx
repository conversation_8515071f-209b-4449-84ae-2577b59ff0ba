import React, { useState } from 'react';
import { 
  ButtonGroup, 
  ActionButton, 
  PageHeaderButtons, 
  FormButtons,
  DataTableButtons
} from '@/components/ui/ButtonStandardization';
import {
  SearchBar,
  FilterBar,
  FilterDropdown,
  FilterOption,
  FilterPanel,
  ActiveFilters
} from '@/components/ui/FilterStandardization';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Plus, 
  Filter, 
  Search, 
  Edit, 
  Trash, 
  Save, 
  RefreshCw, 
  Download, 
  Upload 
} from 'lucide-react';
import PageTransition from '@/components/layout/PageTransition';

const StyleGuideDemo = () => {
  // State for demo components
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [propertyFilter, setPropertyFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [loading, setLoading] = useState(false);

  // Demo active filters
  const activeFilters = [
    { id: 'status', label: 'Status', value: statusFilter !== 'all' ? statusFilter : '' },
    { id: 'property', label: 'Property', value: propertyFilter !== 'all' ? propertyFilter : '' },
  ].filter(filter => filter.value);

  const handleRemoveFilter = (id: string) => {
    if (id === 'status') setStatusFilter('all');
    if (id === 'property') setPropertyFilter('all');
  };

  const handleClearFilters = () => {
    setStatusFilter('all');
    setPropertyFilter('all');
  };

  // Demo loading state
  const handleLoadingDemo = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <PageTransition>
      <div className="container mx-auto px-4 py-8 pb-32">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">UI Style Guide</h1>
          <p className="text-muted-foreground">
            This page demonstrates the standardized UI components for StayFuse.
          </p>
        </div>

        {/* Button Standards Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Button Standards</CardTitle>
            <CardDescription>
              Standardized button components and their usage
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* Button Variants */}
            <div>
              <h3 className="text-lg font-medium mb-4">Button Variants</h3>
              <div className="flex flex-wrap gap-4">
                <Button>Default (Primary)</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="destructive">Destructive</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="link">Link</Button>
              </div>
            </div>

            {/* Button Sizes */}
            <div>
              <h3 className="text-lg font-medium mb-4">Button Sizes</h3>
              <div className="flex flex-wrap items-center gap-4">
                <Button size="lg">Large</Button>
                <Button>Default</Button>
                <Button size="sm">Small</Button>
                <Button size="icon"><Plus className="text-foreground" /></Button>
              </div>
            </div>

            {/* Button with Icons */}
            <div>
              <h3 className="text-lg font-medium mb-4">Buttons with Icons</h3>
              <div className="flex flex-wrap gap-4">
                <Button>
                  <Plus className="h-4 w-4 text-primary-foreground" />
                  Add New
                </Button>
                <Button variant="outline">
                  <Download className="h-4 w-4 text-foreground" />
                  Download
                </Button>
                <Button variant="secondary">
                  <Upload className="h-4 w-4 text-secondary-foreground" />
                  Upload
                </Button>
                <Button variant="destructive">
                  <Trash className="h-4 w-4 text-destructive-foreground" />
                  Delete
                </Button>
              </div>
            </div>

            {/* Button Groups */}
            <div>
              <h3 className="text-lg font-medium mb-4">Button Groups</h3>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Right-aligned (default for forms)</p>
                  <ButtonGroup position="right">
                    <Button variant="outline">Cancel</Button>
                    <Button>Save</Button>
                  </ButtonGroup>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Left-aligned</p>
                  <ButtonGroup position="left">
                    <Button variant="outline">Back</Button>
                    <Button>Next</Button>
                  </ButtonGroup>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Center-aligned</p>
                  <ButtonGroup position="center">
                    <Button variant="outline">Cancel</Button>
                    <Button>Confirm</Button>
                  </ButtonGroup>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Space-between</p>
                  <ButtonGroup position="between">
                    <Button variant="outline">Back</Button>
                    <Button>Next</Button>
                  </ButtonGroup>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div>
              <h3 className="text-lg font-medium mb-4">Action Buttons</h3>
              <div className="flex flex-wrap gap-4">
                <ActionButton icon={<Save className="h-4 w-4 text-foreground" />}>
                  Save Changes
                </ActionButton>
                <ActionButton
                  icon={<RefreshCw className="h-4 w-4 text-foreground" />}
                  loading={loading}
                  onClick={handleLoadingDemo}
                >
                  Refresh Data
                </ActionButton>
                <ActionButton
                  icon={<Trash className="h-4 w-4 text-foreground" />}
                  variant="destructive"
                >
                  Delete Item
                </ActionButton>
              </div>
            </div>

            {/* Page Header Buttons */}
            <div>
              <h3 className="text-lg font-medium mb-4">Page Header Buttons</h3>
              <div className="flex justify-between items-center p-4 border rounded-lg">
                <div>
                  <h2 className="text-xl font-semibold">Page Title</h2>
                  <p className="text-sm text-muted-foreground">Page description</p>
                </div>
                <PageHeaderButtons>
                  <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)}>
                    <Filter className="h-4 w-4 mr-2" />
                    Filters
                  </Button>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add New
                  </Button>
                </PageHeaderButtons>
              </div>
            </div>

            {/* Form Buttons */}
            <div>
              <h3 className="text-lg font-medium mb-4">Form Buttons</h3>
              <div className="border rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" placeholder="Enter name" />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" placeholder="Enter email" />
                  </div>
                </div>
                <FormButtons 
                  onCancel={() => console.log('Cancel')} 
                  onSubmit={handleLoadingDemo}
                  loading={loading}
                  submitLabel="Save Changes"
                />
              </div>
            </div>

            {/* Data Table Buttons */}
            <div>
              <h3 className="text-lg font-medium mb-4">Data Table Buttons</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>Example Item</TableCell>
                    <TableCell>Active</TableCell>
                    <TableCell className="text-right">
                      <DataTableButtons>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" className="text-destructive">
                          <Trash className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </DataTableButtons>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Filter Standards Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Filter Standards</CardTitle>
            <CardDescription>
              Standardized filter components and their usage
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* Search Bar */}
            <div>
              <h3 className="text-lg font-medium mb-4">Search Bar</h3>
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="Search items..."
              />
            </div>

            {/* Filter Bar */}
            <div>
              <h3 className="text-lg font-medium mb-4">Filter Bar</h3>
              <FilterBar>
                <FilterDropdown
                  label="Status"
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <FilterOption value="all">All Statuses</FilterOption>
                  <FilterOption value="active">Active</FilterOption>
                  <FilterOption value="inactive">Inactive</FilterOption>
                  <FilterOption value="pending">Pending</FilterOption>
                </FilterDropdown>
                
                <FilterDropdown
                  label="Property"
                  value={propertyFilter}
                  onChange={setPropertyFilter}
                >
                  <FilterOption value="all">All Properties</FilterOption>
                  <FilterOption value="beach-house">Beach House</FilterOption>
                  <FilterOption value="mountain-cabin">Mountain Cabin</FilterOption>
                  <FilterOption value="city-apartment">City Apartment</FilterOption>
                </FilterDropdown>
              </FilterBar>
            </div>

            {/* Active Filters */}
            <div>
              <h3 className="text-lg font-medium mb-4">Active Filters</h3>
              <ActiveFilters
                filters={activeFilters}
                onRemove={handleRemoveFilter}
                onClear={handleClearFilters}
              />
              <p className="text-sm text-muted-foreground mt-2">
                Try selecting filters above to see active filters appear here
              </p>
            </div>

            {/* Filter Panel */}
            <div>
              <h3 className="text-lg font-medium mb-4">Filter Panel</h3>
              <FilterPanel title="Advanced Filters" defaultOpen={true}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="property-filter">Property</Label>
                    <Select value={propertyFilter} onValueChange={setPropertyFilter}>
                      <SelectTrigger id="property-filter">
                        <SelectValue placeholder="Select property" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Properties</SelectItem>
                        <SelectItem value="beach-house">Beach House</SelectItem>
                        <SelectItem value="mountain-cabin">Mountain Cabin</SelectItem>
                        <SelectItem value="city-apartment">City Apartment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="status-filter">Status</Label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger id="status-filter">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="date-filter">Date Range</Label>
                    <Input id="date-filter" type="date" />
                  </div>
                </div>
                <ButtonGroup position="right" className="mt-4">
                  <Button variant="outline" onClick={handleClearFilters}>
                    Reset Filters
                  </Button>
                  <Button>
                    Apply Filters
                  </Button>
                </ButtonGroup>
              </FilterPanel>
            </div>

            {/* Complete Page Example */}
            <div>
              <h3 className="text-lg font-medium mb-4">Complete Page Example</h3>
              <div className="border rounded-lg p-6 space-y-6">
                {/* Page Header */}
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-xl font-semibold">Properties</h2>
                    <p className="text-sm text-muted-foreground">Manage your rental properties</p>
                  </div>
                  <PageHeaderButtons>
                    <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)}>
                      <Filter className="h-4 w-4 mr-2" />
                      Filters
                    </Button>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Property
                    </Button>
                  </PageHeaderButtons>
                </div>

                {/* Search and Filters */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-2">
                    <SearchBar
                      value={searchQuery}
                      onChange={setSearchQuery}
                      placeholder="Search properties..."
                    />
                  </div>
                  <div>
                    <FilterBar>
                      <FilterDropdown
                        label="Status"
                        value={statusFilter}
                        onChange={setStatusFilter}
                      >
                        <FilterOption value="all">All Statuses</FilterOption>
                        <FilterOption value="active">Active</FilterOption>
                        <FilterOption value="inactive">Inactive</FilterOption>
                      </FilterDropdown>
                    </FilterBar>
                  </div>
                </div>

                {/* Active Filters */}
                <ActiveFilters
                  filters={activeFilters}
                  onRemove={handleRemoveFilter}
                  onClear={handleClearFilters}
                />

                {/* Data Table */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>Beach House</TableCell>
                      <TableCell>Miami, FL</TableCell>
                      <TableCell>Active</TableCell>
                      <TableCell className="text-right">
                        <DataTableButtons>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button variant="ghost" size="sm" className="text-destructive">
                            <Trash className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </DataTableButtons>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Mountain Cabin</TableCell>
                      <TableCell>Aspen, CO</TableCell>
                      <TableCell>Active</TableCell>
                      <TableCell className="text-right">
                        <DataTableButtons>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button variant="ghost" size="sm" className="text-destructive">
                            <Trash className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </DataTableButtons>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageTransition>
  );
};

export default StyleGuideDemo;
