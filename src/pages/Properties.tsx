
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import PageTransition from '../components/layout/PageTransition';
import { useAuth } from '@/contexts/AuthContext';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2'; // Using the new hook
import { useQueryClient } from '@tanstack/react-query';
import { usePropertyFilters } from '@/hooks/usePropertyFilters';
import { usePropertyActions } from '@/hooks/usePropertyActions';
import { usePropertyStatistics } from '@/hooks/usePropertyStatistics';
import PropertyHeader from '@/components/properties/PropertyHeader';
import PropertyFilters from '@/components/properties/PropertyFilters';
import PropertyListView from '@/components/properties/PropertyListView';
import AddPropertyDialog from '@/components/properties/AddPropertyDialog';
import { syncAllPropertiesCalendarsIfNeeded } from '@/utils/calendarUtils';
import { StandardLoadingState } from '@/components/ui/StandardizedUI';

const Properties = () => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const navigate = useNavigate();

  // Views state
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid');
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use the new React Query hook
  const { properties, loading, error, fetchProperties } = usePropertiesQueryV2();
  const queryClient = useQueryClient();

  // Function to manually refresh properties - simplified like Operations page
  const handleRefresh = useCallback(async () => {
    if (isRefreshing || loading) {
      console.log('[Properties] Already refreshing or loading, skipping refresh');
      return;
    }

    console.log('[Properties] Manually refreshing properties');
    setIsRefreshing(true);

    try {
      // Clear any existing errors
      document.title = 'Properties - StayFu'; // Reset title in case it was showing an error

      // Call the hook's refresh function directly
      await fetchProperties();

      console.log('[Properties] Properties refreshed successfully');
    } catch (err) {
      console.error('[Properties] Error refreshing properties:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchProperties, isRefreshing, loading]);

  // Create a dummy setProperties function since our new hook doesn't need it
  const setProperties = useCallback(() => {
    console.log('[Properties] setProperties called - using React Query for data management');
    // This is a no-op function since we're using React Query for data management
    // We'll refresh data using fetchProperties instead
  }, []);

  const {
    isAddPropertyOpen,
    setIsAddPropertyOpen,
    newProperty,
    setNewProperty,
    handleAddProperty,
    handleUpdateProperty,
    handleDeleteProperty,
    handleViewProperty
  } = usePropertyActions(userId, properties, setProperties, fetchProperties);

  // Get property IDs for statistics
  const propertyIds = properties.map(p => p.id);

  // Fetch property statistics
  const { statistics, loading: statisticsLoading, error: statisticsError } = usePropertyStatistics(propertyIds);

  // Initialize filters with statistics
  const { filters, setFilters, filteredProperties } = usePropertyFilters(properties, statistics);

  // Add a ref to track initial load
  const initialLoadDoneRef = useRef(false);

  // Log state changes for debugging and handle initial load
  useEffect(() => {
    console.log("Properties component - properties count:", properties.length);
    console.log("Properties component - userId:", userId);
    console.log("Current view type:", viewType);

    // Log detailed property data for debugging
    if (properties.length > 0) {
      console.log("Properties component - sample property data:", {
        name: properties[0].name,
        bedrooms: properties[0].bedrooms,
        bathrooms: properties[0].bathrooms,
        image_url: properties[0].image_url
      });
    }

    // Auto-sync property calendars if needed
    if (!loading && properties?.length > 0 && userId) {
      syncAllPropertiesCalendarsIfNeeded(properties, userId)
        .then(() => {
          console.log('[Properties] Auto-synced property calendars if needed');
        })
        .catch(error => {
          console.error('[Properties] Error auto-syncing property calendars:', error);
        });
    }

    // If this is the first load and we have properties, mark initial load as done
    if (!initialLoadDoneRef.current && properties.length > 0) {
      console.log('[Properties] Initial data load complete');
      initialLoadDoneRef.current = true;
    }
  }, [properties, userId, viewType, loading]);

  // Simplified event handling - removed visibility change handler
  useEffect(() => {
    // Track if we're currently refreshing to prevent duplicate refreshes
    let isCurrentlyRefreshing = false;

    // Function to handle global data refresh events
    const handleDataRefreshed = (event: CustomEvent) => {
      // Only refresh if we're not already refreshing and this event is relevant to us
      if (!isRefreshing && !loading && !isCurrentlyRefreshing) {
        // Get the current route path
        const currentPath = window.location.pathname;

        // Check if this event is for us (either no route specified or includes '/properties')
        const refreshedRoute = event.detail?.route || '';
        if (!refreshedRoute || refreshedRoute === '/properties' || currentPath.includes('/properties')) {
          console.log('[Properties] Received data refresh event, refreshing data');
          fetchProperties(); // Use the hook's refresh function directly
        }
      }
    };

    // Listen for our single, unified data refresh event only
    window.addEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);

    // Return cleanup function
    return () => {
      window.removeEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
    };
  }, [fetchProperties, isRefreshing, loading, queryClient]);

  const handleRetry = () => {
    fetchProperties();
  };





  const handleSearch = (term: string) => {
    setFilters({...filters, searchTerm: term});
  };

  const handleToggleView = (type: 'grid' | 'list') => {
    console.log("Toggling view to:", type);
    setViewType(type);
  };

  return (
    <PageTransition>
      <div className="container mx-auto px-4 pb-32">
        {/* Header with search, filters, and add property button */}
        <div className="glass-card glass-card-hover rounded-xl p-6 mb-6 transition-all duration-300">
          <PropertyHeader
            title="Properties"
            description="Manage your rental properties"
            onAddProperty={() => setIsAddPropertyOpen(true)}
            onToggleView={handleToggleView}
            onToggleFilters={() => setFiltersOpen(!filtersOpen)}
            viewType={viewType}
            filtersOpen={filtersOpen}
            propertyCount={filteredProperties.length}
            onSearch={handleSearch}
            searchQuery={filters.searchTerm}
            onRefresh={handleRefresh}
            isLoading={loading || isRefreshing}
          />
        </div>

        {/* Filters panel */}
        {filtersOpen && (
          <div className="glass-card glass-card-hover rounded-xl p-6 mb-6 transition-all duration-300">
            <PropertyFilters
              filters={filters}
              onFilterChange={setFilters}
              cities={Array.from(new Set(properties.map(p => p.city)))}
            />
          </div>
        )}

        {/* Debug info - commented out for production but can be easily re-enabled */}
        {/* To re-enable debug info, uncomment the following code block */}
        {/*
        {process.env.NODE_ENV === 'development' && (
          <div className="p-4 mb-4 border border-yellow-400 bg-yellow-50 rounded-md">
            <h3 className="font-bold">Debug Info:</h3>
            <div className="text-sm">
              <p>Loading: {loading ? 'true' : 'false'}</p>
              <p>Error: {error || 'none'}</p>
              <p>Properties: {properties.length}</p>
              <p>Filtered Properties: {filteredProperties.length}</p>
              <button
                onClick={handleRefresh}
                className="px-2 py-1 bg-blue-500 text-white rounded mt-2"
              >
                Force Refresh
              </button>
            </div>
          </div>
        )}
        */}

        {/* Property list/grid view */}
        <div className="relative glass-card glass-card-hover rounded-xl p-6 transition-all duration-300">
          {/* Show mini loading state when refreshing but we already have data */}
          {loading && filteredProperties.length > 0 && (
            <StandardLoadingState message="Refreshing properties..." mini={true} />
          )}
          
          <PropertyListView
            loading={loading}
            error={error}
            filteredProperties={filteredProperties}
            properties={properties}
            handleViewProperty={handleViewProperty}
            handleRetry={handleRetry}
            onAddProperty={() => setIsAddPropertyOpen(true)}
            viewType={viewType}
            statistics={statistics}
          />
        </div>

        {/* Add property dialog */}
        <AddPropertyDialog
          isOpen={isAddPropertyOpen}
          onOpenChange={setIsAddPropertyOpen}
          newProperty={newProperty}
          setNewProperty={setNewProperty}
          handleAddProperty={handleAddProperty}
        />


      </div>
    </PageTransition>
  );
};

export default Properties;
