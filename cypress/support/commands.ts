// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom command to login programmatically
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password') => {
  cy.visit('/login');
  cy.get('input[name="email"]').type(email);
  cy.get('input[name="password"]').type(password);
  cy.get('button[type="submit"]').click();
  cy.url().should('include', '/dashboard');
});

// Custom command to create a property
Cypress.Commands.add('createProperty', (propertyData) => {
  cy.visit('/properties');
  cy.get('[data-cy="add-property-btn"]').click();
  cy.get('input[name="name"]').type(propertyData.name);
  cy.get('input[name="address"]').type(propertyData.address);
  cy.get('input[name="city"]').type(propertyData.city);
  cy.get('input[name="state"]').type(propertyData.state);
  cy.get('input[name="zip_code"]').type(propertyData.zip_code);
  cy.get('button[type="submit"]').click();
  cy.contains(propertyData.name).should('be.visible');
});

// Add TypeScript support for custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      login(email?: string, password?: string): Chainable<void>;
      createProperty(propertyData: Record<string, unknown>): Chainable<void>;
    }
  }
}

export {};
